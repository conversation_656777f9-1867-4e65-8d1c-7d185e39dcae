import PyPDF2
import os
import sys

def extract_text_from_pdf(pdf_path):
    """Extract text from a PDF file"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            # Extract text from all pages
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"
            
            return text
    except Exception as e:
        return f"Error reading {pdf_path}: {str(e)}"

def extract_metadata_from_pdf(pdf_path):
    """Extract metadata from a PDF file"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            metadata = pdf_reader.metadata
            
            info = {}
            if metadata:
                info['Title'] = metadata.get('/Title', 'N/A')
                info['Author'] = metadata.get('/Author', 'N/A')
                info['Subject'] = metadata.get('/Subject', 'N/A')
                info['Creator'] = metadata.get('/Creator', 'N/A')
                info['Producer'] = metadata.get('/Producer', 'N/A')
                info['CreationDate'] = metadata.get('/CreationDate', 'N/A')
            
            info['Pages'] = len(pdf_reader.pages)
            return info
    except Exception as e:
        return f"Error reading metadata from {pdf_path}: {str(e)}"

def main():
    # List of PDF files
    pdf_files = ['p1.pdf', 'p2.pdf', 'p3.pdf', 'p4.pdf', 'p5.pdf', 'p6.pdf']
    
    for pdf_file in pdf_files:
        if os.path.exists(pdf_file):
            print(f"\n{'='*60}")
            print(f"ANALYZING: {pdf_file}")
            print(f"{'='*60}")
            
            # Extract metadata
            print("\nMETADATA:")
            print("-" * 30)
            metadata = extract_metadata_from_pdf(pdf_file)
            if isinstance(metadata, dict):
                for key, value in metadata.items():
                    print(f"{key}: {value}")
            else:
                print(metadata)
            
            # Extract text (first 2000 characters to avoid overwhelming output)
            print("\nTEXT CONTENT (First 2000 characters):")
            print("-" * 50)
            text = extract_text_from_pdf(pdf_file)
            if len(text) > 2000:
                print(text[:2000] + "...\n[TRUNCATED]")
            else:
                print(text)
                
            # Save full text to a separate file
            output_file = f"{pdf_file}_extracted.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"METADATA:\n")
                f.write("-" * 30 + "\n")
                if isinstance(metadata, dict):
                    for key, value in metadata.items():
                        f.write(f"{key}: {value}\n")
                else:
                    f.write(str(metadata) + "\n")
                f.write(f"\nFULL TEXT:\n")
                f.write("-" * 30 + "\n")
                f.write(text)
            
            print(f"\nFull text saved to: {output_file}")
        else:
            print(f"File {pdf_file} not found!")

if __name__ == "__main__":
    main()
