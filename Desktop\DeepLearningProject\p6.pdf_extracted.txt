METADATA:
------------------------------
Title: N/A
Author: N/A
Subject: N/A
Creator: N/A
Producer: iText® 5.5.8 ©2000-2015 iText Group NV (SPRINGER SBM; licensed version)
CreationDate: D:20240112030320+01'00'
Pages: 14

FULL TEXT:
------------------------------
Vol.:(**********)SN Computer Science           (2024) 5:190  
https://doi.org/10.1007/s42979-023-02542-1
SN Computer Science
ORIGINAL RESEARCH
Enhancing Video Anomaly Detection Using Spatio‑Temporal 
Autoencoders and Convolutional LSTM Networks
Ghayth Almahadin1 · Maheswari Subburaj2  · <PERSON> · <PERSON><PERSON>thasivam Singaram4 · 
<PERSON><PERSON><PERSON> · Pankaj Dadheech6 · Amol D. Vibhute7 · <PERSON><PERSON><PERSON>gan8
Received: 10 September 2023 / Accepted: 11 November 2023 
© The Author(s), under exclusive licence to Springer Nature Singapore Pte Ltd 2024
Abstract
Identifying suspicious activities or behaviors is essential in the domain of Anomaly Detection (AD). In crowded scenes, 
the presence of inter-object occlusions often complicates the detection of such behaviors. Therefore, developing a robust 
method capable of accurately detecting and locating anomalous activities within video sequences becomes crucial, especially 
in densely populated environments. This research initiative aims to address this challenge by proposing a novel approach 
focusing on AD behaviors in crowded settings. By leveraging a spatio-temporal method, the proposed approach harnesses 
the power of both spatial and temporal dimensions. This enables the method to effectively capture and analyze the intricate 
motion patterns and spatial information embedded within the continuous frames of video data. The objective is to create a 
comprehensive model that can efficiently detect and precisely locate anomalies within complex video sequences, specifically 
those featuring human crowds. The efficacy of the proposed model will be rigorously evaluated using a benchmark dataset 
encompassing diverse scenarios involving crowded environments. The dataset is designed to simulate real-world conditions 
where millions of video footage need to be continuously monitored in real time. The focus is on identifying anomalies, which 
might occur within short time frames, sometimes as brief as five minutes or even less. Given the challenges posed by the 
massive volume of data and the requirement for rapid AD, the research emphasizes the limitations of traditional Supervised 
Learning (SL) methods in this context.
Keywords Machine learning · Anomaly detection · LSTM · Autoencoders · Spatio-temporal features · Suspicious activities
Introduction
With technological advancements and the proliferation of 
surveillance cameras in various public settings, ensuring 
the safety and well-being of individuals has become a pri -
mary concern. In this context, AD behaviors have emerged 
as a critical area of research within surveillance systems 
and Computer Vision (CV) [1 –5]. While Anomaly Detec-
tion (AD) has undergone extensive investigation over the 
past decade, there remains ample scope for further refine-
ment, particularly in extended video surveillance. The 
complexity of distinguishing anomalies is compounded by 
the influence of contextual factors, where normal behav -
ior in one setting may appear aberrant in another. Given the challenge of annotating diverse events, constructing a 
comprehensive model encompassing all potential anomalies 
proves impractical [6 –10]. To address these challenges, this 
research implements an automated framework for detect-
ing and segmenting pertinent sequences, leveraging a Deep 
Learning (DL) approach for efficient feature extraction and 
representation from video data [11– 15]. By harnessing the 
capabilities of convolutional autoencoders and temporal 
autoencoders, this study seeks to improve AD efficiency, 
enabling the identification of irregular patterns within vast 
volumes of video footage in a timely and accurate manner. 
Any dataset of videos will be significant; the videos will 
have noise associated with them and hold massive events 
and exchanges.
Additionally, context plays a significant role in the 
interpretation of anomaly. For instance, running around 
freely in a park is perfectly normal behaviour, whereas 
doing the same thing in a restaurant is likely to be regarded 
as highly unusual [16– 19]. The concept of an anomaly is This article is part of the topical collection “Soft Computing in 
Engineering Applications” guest edited by Kanubhai K. Patel.
Extended author information available on the last page of the article
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
 SN Computer Science           (2024) 5:190 
   190  Page 2 of 13
SN Computer Science
notoriously tricky to pin down and define precisely. When 
the scope is delineated, AD functions most effectively.
Regarding successful AD recognition in the past, the 
primary factor was labelled video footage with clearly 
defined events of interest. This footage mainly focuses on 
defining the events; hence, sequences, including crowded 
locations, were scarce. It would be prohibitively expen-
sive to label every possible event type. In short, learning 
a model constituting all the abnormal and irregular events 
is impossible [20– 25].
In this work, using an automated framework for detect-
ing and segmenting sequences of interest, we aim to 
improve upon earlier developed labelling methods while 
reducing the labor-intensive effort required. The video 
data are represented using a set of standard features 
implied automatically from extended video footage with 
the help of a Deep Learning (DL) approach. Processing 
video frames unsupervised can be made easier with a Deep 
Neural Network (DNN) built on convolutional autoencod-
ers. They identify spatial structures in data. These struc-
tures are then grouped to compose the proposed video 
representation. After confirming spatial structures, the 
representation is sent to the temporal autoencoders to learn 
regular temporal patterns. Feature Extraction (FE) [ 26–28] 
representation of video is combined with learning those 
feature models. The ability to automatically fine-tune the 
model according to the video allows us to be domain-free 
and reduce computational time.
AD helps us to find data points that are chaotic and 
unexpected. The primary focus is to distinguish regular 
patterns from anomalous ones [29– 31]. The solution to 
this problem might come quickly as Binary Classification 
(BC), and many might think that they can solve it using 
any Supervised Learning (SL) algorithm, but the classes 
can be highly imbalanced [32].
Millions of minutes of footage need to be reviewed in 
real-time, out of which there could be an anomaly for even 
5 min or fewer. SL would suffer in this scenario. However, 
Autoencoders can be perfect as we can train them on nor -
mal parts and not use annotated data. The output of the 
autoencoder, when compared to the input, will give us a 
difference based on FE. The more distinct and significant 
the difference, the higher the chances of the input holding 
an anomaly.
The paper follows a structured organization compris-
ing of “Introduction” describing an introduction, “Related 
works ” discussing the related works, “Proposed method-
ology” proposing a methodology encompassing dataset 
details and various techniques, “Results and discussion” 
discusses the results and discussions, and “ Conclusion 
and future work” concludes with future research direc-
tions. It maintains a well-defined format for clarity and 
comprehensibility.Related Works
The article “Robust Real-Time Unusual Event Detection 
Using Multiple Fixed-Location Monitors” presented a 
method for identifying specific types of events that are 
not typical [33– 35]. Their algorithm relies on a collection 
of multiple local monitors that each collect low-level data 
analysis as its starting point. If any one of the local moni -
tors detects something out of the ordinary with their meas-
urements, an alarm will sound [36– 38]. “Abnormal Event 
Detection at 150 FPS in MATLAB” provided a method for 
detecting abnormal events based on using sparse-combina-
tion learning [ 39, 40] so that we can accelerate the testing 
process without compromising the accuracy [41– 44].
In contrast to modern CNN models, this paper uses 
multiple layers of cells without a shared weight for unsu-
pervised visual pattern recognition. Motivated by the ani-
mal visual cortex 50 years ago, CNN excels at image and 
video spatial feature extraction [45].
An approach to learning periodic patterns with autoen-
coders and minimal supervision. The research paper 
“Learning temporal regularity in video sequences” [4 –6] 
implemented a fully convolutional autoencoder to learn 
local features and classifiers within the context of a single 
learning framework [46– 49].
The purpose of this article by [50], “AD in crowded 
scenes,” is to present a framework for AD in crowded 
scenes. They used an MDT-based model in their imple-
mentation. “Abnormal crowd behaviour detection using 
social force model” is the title of the author’s paper in 
which they developed a model to detect and localize 
abnormal behaviors in crowd scenes using the social force 
model [51]. Using a bag of words methodology, they clas-
sified frames as normal or abnormal [52].
The author researched CV and Pattern Recogni-
tion (PR) for their “Real-Time AD and Localization in 
Crowded Scenes” paper. In crowded scenes, they sug-
gested a technique for real-time AD and localization. [53] 
We defined each video as a collection of non-overlapping 
cubic patches and used local and global descriptors to 
describe them.
The objective of the research titled “Enhancing Video 
AD Using Spatiotemporal Autoencoders and Convolu-
tional LSTM Networks” is to improve the efficiency and 
accuracy of AD within video data by integrating the capa-
bilities of Spatiotemporal Autoencoders and Convolutional 
Long Short-Term Memory (LSTM) networks. The aim is 
to develop a robust and sophisticated framework that can 
effectively capture spatial and temporal information within 
video sequences, enabling the identification and localiza-
tion of anomalous activities with higher precision in vari-
ous surveillance and security applications [54].
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
SN Computer Science           (2024) 5:190  
 Page 3 of 13   190 
SN Computer Science
Table  1 provides an overview comparison of the numer -
ous methodologies discussed in this chapter.
Proposed Methodology
Dataset
This work used the USCD-AD dataset to train and test 
this proposed model. The videos included in this dataset 
were recorded using a fixed camera placed at a height that 
gave it a view that extended over walkways used by pedes-
trians. There are times when these walkways are not very 
crowded at all, and there are also times when the crowd 
density is at an all-time high. Only pedestrians are permitted at normal events. Abnormal occurrences can be attributed 
to either:
• Golf carts, bicycles, and skateboards are examples of 
non-pedestrian entities permitted to use the walkways.
• Certain motion patterns of pedestrians that were not 
observed Dataset link—UCSDped1 [https:// drive. google.  
com/ drive/ folde rs/ 1JJY8 FzXjV  gysOl aTqHv moSEP  
dUhu_ VUZ? usp= shari ng].
The following flow chart in Fig.  1 defines this model 
pipeline for AD.
The problem of AD in videos can be considered a Binary 
Classification (BC) issue. It requires labelled data, which is 
difficult to collect for the reasons below.
Table 1  Analyses of the differences between various methods of feature processing
Models Benefits Drawbacks
VGG16 [55] It decreases the size of the kernel while simultaneously 
increasing the depth of conventional CNNsThe only features that can be extracted are spatial ones
DenseNet [56] There has been an improvement made to the relation-
ships between convolution layers
The model requires fewer feature images and input 
parameters
It prevents overfitting well to its excellent performanceThe currently available DL technologies cannot provide 
sufficient support for the DenseNet structure, which 
results in the squandering of GPU memory
LSTM [11] This addresses the issue where the standard RNN cannot 
process long-term videoThe LSTM algorithm requires a more significant and 
faster PC than CNNs do
Two-Stream [57–59] Generating optical flow takes more time and computing 
power than is typically necessary
Compared to the LSTM, the performance of the two-
stream model is significantly higherThe structures that CNN uses to broadcast in two different 
streams are not cutting edge
The camera's motion can easily affect the process of gen-
erating optical flow, which can be problematic
Convolutional 3D [60, 61] It retains some benefits that are typically associated with 
conventional 2D CNNsIt can manage the temporal aspects
Fig. 1  Proposed architecture diagram
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
 SN Computer Science           (2024) 5:190 
   190  Page 4 of 13
SN Computer Science
1. Because of their scarcity, abnormal occurrences are 
notoriously difficult to document.
2. There are a considerable number of unusual events. The 
detection and labelling of such events manually are a 
mammoth task that requires many resources to complete 
successfully.
In Fig.  2, unsupervised methods, such as autoencod -
ers and spatio-temporal features, were favored. These 
approaches outshine their supervised counterparts as they 
solely rely on unlabelled video evidence containing few 
or no abnormal activities, readily available in real-life 
scenarios.
The preprocessing steps for the UCSD AD dataset were 
paramount in optimizing raw video data for model input 
and enhancing overall model performance. First, videos 
were uniformly resized to a consistent spatial resolution, 
typically 227 × 227 pixels or other specified sizes, ensuring 
dataset-wide uniformity. This step maintains compatibility 
with fixed input dimensions. Next, temporal subsampling 
of video frames created coherent input sequences to capture 
critical temporal dependencies, ultimately improving model 
convergence and generalization. Data augmentation tech-
niques, including random horizontal flipping, random crop-
ping, and minor adjustments in brightness and contrast, were 
employed to diversify the training dataset and bolster model robustness. Lastly, pixel values were normalized to a spe-
cific range, stabilizing training and facilitating model con-
vergence. These pre-processing steps prove indispensable 
in enhancing the model’s performance in video AD tasks.
Convolution Neural Network (CNN)
A CNN is a Deep Learning (DL) algorithm that takes its 
input in the form of an image and assigns importance to 
technical approaches or objects in the image by updating its 
weights and biases in such a case that the objects that are 
present can differ significantly from one another and then 
outputs the results of this method.
Convolutional Neural Network (CNN) is constructed to 
resemble the connection patterns of the Neurons present in 
the human brain. Because it allows for a reduction in the 
total range of parameters and the reusability of weights, this 
layout is the one that works best for an image dataset. The 
network can understand the minute details of any image bet-
ter. CNN condenses images into a format that is simpler 
to process while simultaneously preserving the details with 
the highest possible degree of accuracy. By employing the 
appropriate FE methods, a CNN can successfully capture 
the Spatial–Temporal dependencies that exist in an image.
The convolutional layer is the preliminary step in con-
structing CNN in Fig.  3, which comprises several layers in 
Fig. 2  Samples from the data-
set. The red boxes define the 
anomaly
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
SN Computer Science           (2024) 5:190  
 Page 5 of 13   190 
SN Computer Science
total. It is the core building block, and most of the computa-
tions are carried out by it. This work uses filters or kernels to 
convolve data or images. Filters are applied across the data. 
It is implemented through a sliding window. For each slid-
ing action, the element-wise product of the image’s filters is 
computed, and the result is added together. The result of a 
convolution employing a 3-D filter with color will be a 2-D 
matrix in its representation.
Rectified Linear Unit (ReLU) is the name of the function 
that the subsequent Activation Layer (AL) uses. Applying 
the rectifier function at this stage will increase the network’s 
level of non-linearity. This is necessary because images are 
constructed from features that are frequently not linear to 
one another.
The Pooling Layer (PL) comes after the AL and involves 
a down-sampling of features. This layer follows the AL. The 
3-D volume performs down-sampling on each layer. This 
layer includes the hyperparameters for the dimensions of 
spatial extent and stride, and they can be found here. The 
dimension of spatial extent is the value of ‘n ’, which can take 
‘N’ representations of crosses and features and map them to 
a single value. The sliding window will skip a certain num-
ber of features along the width and the height depending on 
the “stroke,” which is the number of features it will skip. The 
Fully Connected (FC) layer is the final layer, and it is the one 
that produces the output.
The input has been transformed into a single column 
because of this work, which flattens the input, which is the 
pooled feature map matrix in its entirety. Following that, the 
Neural Network (NN) will begin processing this informa-
tion. This research combines all these features to create a 
model. This work uses the Sigmoid or SoftMax activation 
function to classify the output. The deconvolutional layers 
densify the sparse signals. Multiple learned filters are used 
to perform operations that are like convolution. By operating 
in reverse convolution, they could link a single input activa-
tion to patch outputs.Long Short‑Term Memory (LSTM)
In DL, a Recurrent Neural Network (RNN) known as an 
LSTM in Fig.  4 has been developed specifically to address 
issues related to sequential prediction. In contrast to simple 
recurrent networks, LSTMs are not hindered by the optimi-
zation challenges that prevent them from capturing long-
term temporal dependencies. The structure of an LSTM is 
like that of a chain, consisting of four layers that interact 
with one another.
A full vector is transmitted along each line from the 
node’s output to the inputs of other nodes. A cell state with 
only a single insignificant interaction is held by the LSTM, 
the horizontal line that runs through the top of the diagram. 
Unaltered information can travel along it. Using only these 
regulated gates, LSTM could either remove information 
from the cell state or add information to it. Each cell state 
is protected and controlled by three of these gates in the 
LSTM. Figure  5 shows the legends for the LSTM model.
Working of LSTM
A. Stage 1: Forget Gate (FG) Layer: It is made by a sig-
moid layer. It decides what information should be removed 
from the cell state. Considerations of ht − 1 and xt are made, 
and a number between 0 and 1 are returned. It is done for Fig. 3  Architecture of a CNN
Fig. 4  LSTM architecture
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
 SN Computer Science           (2024) 5:190 
   190  Page 6 of 13
SN Computer Science
each number in the cell state Ct − 1. 1 stands for “total reten-
tion,” while a 0 represents “no retention”, Eq. (1 ):
B. Stage 2: Input Gate (IG) Layer:  It is made by a sigmoid 
and a tanh layer. It determines what new data should be 
saved as part of the cell’s current state. The sigmoid layer 
determines the values we will update. A set of candidate 
values, Ct, for the state is produced by the tanh layer. To 
generate an update to the step [Eq. (2 )], the two decisions 
are consolidated into one.
Following the input gate layer, we must transition from 
the previous cell state Ct1 to the subsequent Ct.
We multiply the previous state by ft but forget what we 
decided to let go of. After that, we multiply by ( it * Ct). 
These are the new candidate values, and their magnitude is 
determined by the degree to which we would like each state 
value to be updated, Eq. (3 ):
C. Stage 3: Final Filtered Output:  A sigmoid layer is 
responsible for its generation. This layer determines which 
aspects of the cell state will be included in the output. After 
the sigmoid gate’s output is settled, the cell state is multi-
plied by the result of tanh to move the values to the range 
[− 1, 1]. As a result, we only output the portion that we 
choose to output, Eq. (4 ):
D. Autoencoder: One device that combines the functions 
of an encoder and a decoder into a single unit is known as 
an autoencoder. After receiving the input, the encoder will 
encode the data utilizing a reduced representation. On the 
other hand, the decoder works to recreate the initial input 
using the encoded version of the reduced representation. The (1) ft=/u1D70E(Wf⋅[ht−1,xt]+bf).
(2)it=/u1D70E(Wi⋅[ht−1,x1]+bi
/uni0302.s1Ct=tanh(Wc ⋅[ht−1,x1]+bc
(3) Ct=ft×Ct−1+it×/uni0303.s1Ct.
(4)ot=/u1D6FC(W0[ht−1,x1]+b0,
ht=ot×tanh(Ct).Autoencoder is required to learn a sparse representation of 
the training data due to the constraints imposed by the net-
work. Because it is a UL method, the autoencoder is the best 
algorithm for dealing with this AD issue.
E. Working of the Encoder–Decoder
 (i) The Encoder: Learning representations of the input 
data (x ), known as the encoding f , is where it shines 
(x). The name for the encoder’s final layer, known as 
the bottleneck, comes from its function. When f is 
the final input representation, this holds (x ).
 (ii) The Decoder: By introducing the use of the encoding 
that is presented in the bottleneck, it creates a recon-
struction of the input data denoted by r = g(f(x)).
The autoencoder was used in this study to learn patterns 
of regularity in video sequences. The trained autoencoder 
is thought to reconstruct regular video sequences with low 
error but fails to accurately reconstruct motions in irregular 
video sequences. This distinction will aid in the identifica-
tion of anomalies.
F. Convolutional Autoencoder—CAE
The train dataset contains images that are in the 158 × 238-
pixel format. These pictures have had their dimensions nor -
malized and rescaled to be exactly 100 × 100. This dataset, 
along with the batch size and the shuffle factor, is loaded into 
the function known as the data loader.
The encoder comprises two convolutional layers, each 
with a kernel size 32 and 2-MaxPooling2D layers. The 
encoder and the decoder are connected by a dense layer of 
FC, containing 2000 neurons that are not visible to the naked 
eye. The encoder and decoder are connected via this layer. 
The greater the size of this bottleneck, the more informa-
tion can be reconstructed, which opens the possibility of 
recognizing minute details. The decoder produces the final 
signal using a 1-neuron output layer, 32-kernel deconvolu-
tion layers, and 2-up sampling layers. In the output layer of 
the algorithm is where the implementation of the Sigmoid 
function can be found.
In Fig.  6, the network is initialized using the Xavier algo-
rithm. Adam optimizer is used, and the learning rates are 
defined. The batch size for this autoencoder is 32, and it 
has been trained for 30 epochs. The trained model is kept 
for future use and is subsequently called upon for testing. 
The test dataset is also resized equivalently before testing. 
Resizing data is not mandatory; the autoencoder can handle 
variable input sizes, but having a uniform dataset makes the 
computation relatively easy.
Test images are iterated over while the difference between 
input and output is calculated. The differences motivate the 
development of a 4 × 4 convolution kernel pixel map. It is an 
Fig. 5  Legend for the LSTM model
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
SN Computer Science           (2024) 5:190  
 Page 7 of 13   190 
SN Computer Science
anomaly if the pixel value is higher than 1020, equal to 255 
times four. The highest possible value for each pixel equals 
4 × 4 times 255. They will be marked only when the pixels’ 
neighbouring pixels are also abnormal.
Autoencoder has moderately improved pedestrian 
reconstruction but has trouble with novel objects. The 
value of information transmitted between the encoder and 
the decoder will be determined by the dimension of the 
bottleneck layer. Quite good image reconstruction occurs 
if we make it too big or remove it altogether.
The same model was defined in this work; however, 
the dense layer was eliminated and trained once more. It 
is much easier for the network to reconstruct pedestrians 
and other objects without a bottleneck layer, but it is now 
more difficult to spot anomalies.
In the training process for the model, the choice of 
optimizer was Adam, a widely used optimization algo-
rithm with a fixed learning rate of 0.001. A learning rate 
schedule was incorporated, reducing the learning rate by 
a factor of 0.1 if the validation loss plateaued for a prede-
fined number of epochs, allowing dynamic adjustment as 
training progressed. Convergence criteria were set based 
on monitoring the validation loss; training was terminated 
early if the loss did not exhibit significant improvement for 
a specified number of epochs to prevent overfitting. These 
training specifics are crucial for replicating the results and 
laying the groundwork for further advancements in video 
AD.
G. Spatio-Temporal Stacked Frame Autoencoder—STAE
The typical CAE ignores the timeliness of images in a 
sequence. The motion of a person walking or running behind 
other people, in addition to the motion of a bicycle or golf 
cart, is challenging to detect. Instead of one image at a time, 
researchers now consider ‘n’  images. Difference between 
CAE and STAE input format: [batch size, 1, width, height] 
vs. [batch size, n, width, height].
The original input dataset must be updated to include 
‘n’ channels rather than just 1. Since this model uses many 
parameters, it needs a good deal of training data. This research 
uses temporal data augmentation to create many images for training. Concatenating frames with multiple skipping strides 
allow us to generate more training sequences. A succession of 
frames can be fed into the encoder as its input source. Encoder 
architecture consists of the spatio-temporal encoders working 
together. They are called in the above order, one after the other. 
After being encoded, the sequence features are sent to the tem-
poral encoder so that motion can be encoded. It happens after 
the sequence has been output from the spatial encoder.
The video sequence can be reconstructed with the help of 
the decoder, which acts similarly to the encoder. Training Deep 
Neural Networks (DNN) requires a significant financial invest-
ment. The batch normalization procedure is used to bring the 
activities of the neurons up to a consistent level to cut down 
on the amount of time needed for training. The encoder is con-
structed using two MaxPooling layers and three convolutional 
layers. After each layer comes a batch normalization layer to 
adjust the values. The decoder consists of two sampling lay -
ers and three deconvolutions. In the decoder, too, we follow 
each layer with a batch normalization layer. The final output 
layer has 10 channels with the sigmoid activation. The model 
is compiled with previously mentioned hyperparameters and 
trained for 30 epochs. After training, we load the saved model 
and test it on the test dataset. This model can now detect 
motion correctly, which the earlier failed to achieve.
H. Spatio-Temporal Autoencoder Convolutional LSTM
Through the application of convolutional LSTM cells, this 
work can potentially advance the previously developed 
model. Convolutional LSTM layers were used in place of 
fully connected LSTM layers. The inability of FC-LSTMs 
to store spatial data exceptionally well is caused by total 
connections in input-to-state and state-to-state transitions. 
During these transitions, no spatial information is encoded.
The spatio-encoder has two convolutional layers for its 
computations. The temporal encoder and decoder consist of 
one convolutional LSTM layer each. This model bottleneck 
layer is a convolutional LSTM layer. The spatio-decoder 
consists of two deconvolutional layers, and lastly, there is 
the sigmoid-based output layer. Each layer is now normal-
ized by layer normalization instead of batch normalization, 
as we use RNNs in Fig.  7.Fig. 6  Architecture of convolu-
tional autoencoder
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
 SN Computer Science           (2024) 5:190 
   190  Page 8 of 13
SN Computer Science
Results and Discussion
The L2 norm is used to calculate the reconstruction error of 
the intensity value I of a pixel at any point (x , y) in the frame 
‘t’ of the video. The distance between the vector coordinates 
and the vector space’s origin is what the L2 norm attempts 
to compute. The Euclidean distance is another name for this 
concept, Eq. (5 ):
fw, represents the trained model that has learned the training 
dataset using the LSTM convolutional autoencoder.
The following equation explains the reconstruction error.
The errors in the reconstruction are determined by adding 
all these pixels while making mistakes. If the sequence starts 
at ‘t’, then the following equation can be used to determine the 
cost of reconstructing a 10-frame sequence:(5) e(x,y,t)=/uni2016.x/uni2016.xI(x,y,t)− fwI(x,y,t)/uni2016.x/uni2016.x2,
(6)e(t)=/uni2211.s1
x,yex,y,tThe anomaly or abnormality score (denoted by Sa(t)) is 
computed by scaling the cost between 0 and 1. It is com-
puted according to the following equation:
The regularity score (denoted by Sr(t)) is then calculated 
by subtracting the AD scores from 1, Eq. (9 ):
The regularity scores are plotted as shown.
In Fig.  8, the minima or the dips in the graph are the 
frames where anomalies are detected.
The determination of the AD threshold in this research 
involved a manual approach guided by domain-specific 
considerations. This manual threshold selection allowed 
for adaptation to the dataset’s characteristics and the opera-
tional requirements of the application. Incorporating domain 
expertise, the definition of anomalies within this context was 
carefully outlined to ensure interpretability and alignment 
with real-world scenarios. However, it is important to note 
that manual threshold setting is subject to sensitivity and 
potential subjectivity. To address this, sensitivity analyses 
were conducted to evaluate the threshold’s impact on model 
performance. This tailored approach aimed to balance FP 
and FN, ensuring optimal AD results within this unique 
application (Fig.  9).
Future researchers are encouraged to assess whether 
manual or automated threshold selection is most suitable 
for their specific use cases.(7) Seq_Reconstruction_Cost (t)=t+10/uni2211.s1
t�=te(t�).
(8)sa(t)
=Seq_Reconstrcution_Cost (t)−Seq_Reconstruction_Cost (t)Min
Seq_Reconstruction_Cost (t)Max.
(9) sr(t)=1 −sa(t).
Fig. 7  Architecture of spatio-temporal convolutional LSTM autoen-
coder
Fig. 8  Plot for regularity score
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
SN Computer Science           (2024) 5:190  
 Page 9 of 13   190 
SN Computer Science
Predictions
a. Convolutional Autoencoder (CAE)
The model barely recognizes the object from the video 
(Fig.  10).
b. Spatio-Temporal Stacked Frame Autoencoder-STAE
The model recognizes the temporal dependencies, and 
motion is detected accurately. As soon as pedestrians stop 
walking, they are no longer highlighted and vice-versa 
(Fig.  11).
c. Spatio-Temporal Autoencoder Convolutional LSTM
Fig. 9  Regularity score for test dataset—Test 010
Fig. 10  Predictions from the 
convolutional autoencoder 
model
Fig. 11  Predictions from the 
spatio-temporal autoencoder 
model
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
 SN Computer Science           (2024) 5:190 
   190  Page 10 of 13
SN Computer Science
The straight-line highlight in the image showcases how 
the model can follow the anomaly. It tracks the path of the 
cyclist (Fig.  12).
Understanding the limitations of the proposed video AD 
models is crucial for practical applications. These models 
may produce False Positives (FP) in scenarios involving sud-
den environmental changes, crowded scenes, camera dis-
turbances, or partial occlusions, where normal variations or 
abrupt transitions are falsely identified as anomalies. Con-
versely, False Negatives (FN) may occur in gradual anoma-
lies, adaptive behaviors, high variability scenes, or entirely 
novel anomalies that the models have not been trained to 
recognize. These limitations emphasize the need for further 
research to refine model architectures, incorporate contex-
tual information, and establish adaptive thresholding mecha-
nisms to reduce both FP and FN rates, ultimately enhancing 
the reliability of AD models for real-world surveillance and 
AD applications.
Conclusion and Future Work
Video Anomaly Detection (AD) presents an enduring chal-
lenge in computer vision and surveillance, driving continu-
ous efforts to refine existing methodologies. As showcased 
in this study, Deep Learning (DL) has emerged as a potent 
tool for tackling this challenge. The research introduces an 
innovative model that combines spatial Feature Extraction 
(FE) with a temporal sequencer conv-LSTM, effectively 
redefining AD as a spatio-temporal sequence AD prob-
lem. The conv-LSTM layer, notable for its convolutional 
architecture, inherits the merits of Fully Connected LSTMs 
(FC-LSTMs) and demonstrates suitability for analyzing 
spatio-temporal data. The model excels when applied to 
videos capturing routine events from a fixed viewpoint, 
although its performance may exhibit variability depending on scene complexity, occasionally yielding False-Positive 
(FP) outcomes.
Contemplating the future of AD research unveils several 
promising directions. Notably, a strategic plan is to inte-
grate spatio-temporal autoencoder convolutional LSTM and 
employ layer normalization techniques to streamline the 
training process. This research underscores the importance 
of addressing ethical and privacy considerations associated 
with surveillance technologies in tandem with technical 
advancements. A strong commitment to individual privacy 
will be upheld by incorporating anonymization methods and 
privacy safeguards into future AD systems.
Moreover, this work contributes to the drive for stand-
ardization by leveraging established datasets for model 
evaluation, promoting equitable comparisons across diverse 
approaches. Future research will delve into multimodal inte -
gration, enhancing the model’s capacity to process diverse 
data sources effectively. Incorporating human feedback 
mechanisms and prioritizing privacy-preserving measures 
will be pivotal in advancing video AD technology while 
safeguarding individual rights and privacy. The challenges 
and research directions outlined collectively steer the ongo-
ing evolution of video AD.
Funding Not applicable.
Availability of Data and Material Not applicable.
Code Availability Not Applicable.
Declarations  
Conflict of interest Not applicable.Fig. 12  Predictions from the 
LSTM autoencoder model
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
SN Computer Science           (2024) 5:190  
 Page 11 of 13   190 
SN Computer Science
References
 1. Ruwali A, Kumar AJS, Prakash KB, Sivavaraprasad G, Ratnam 
DV. Implementation of hybrid deep learning model (LSTM-CNN) 
for ionospheric TEC forecasting using GPS data. IEEE Geosci 
Remote Sens Lett. 2021;18(6):1004–8.
 2. Kantipudi MVVP, Kumar S, Jha AK. Scene text recognition based 
on bidirectional LSTM and deep neural network. Comput Intell 
Neurosci. 2021;2021:1–11.
 3. Ratnam DV, Rao KN. Bi-LSTM based deep learning method for 
5G signal detection and channel estimation. AIMS Electron Electr 
Eng. 2021;5(4):334–41.
 4. Reddybattula KD, et al. Ionospheric TEC forecasting over an 
Indian low latitude location using long short-term memory 
(LSTM) deep learning network. Universe. 2022;8(11):562.
 5. Enireddy V, Karthikeyan C, Babu DV. OneHotEncoding and 
LSTM-based deep learning models for protein secondary structure 
prediction. Soft Comput. 2022;26(8):3825–36.
 6. Fernandes, Mannepalli K. Speech emotion recognition using 
deep learning LSTM for Tamil language. Pertan J Sci Technol. 
2021;29(3):1915–36.
 7. Fernandes JB, Mannepalli K. Enhanced deep hierarchal GRU 
& BILSTM using data augmentation and spatial features for 
tamil emotional speech recognition. Int J Mod Educ Comput Sci. 
2022;14(3):45–63.
 8. Dharani NP, Bojja P. Analysis and prediction of COVID-19 by 
using recurrent LSTM neural network model in machine learning. 
Int J Adv Comput Sci Appl. 2022;13(5):171–8.
 9. Divya TV, Banik BG. Detecting fake news over job posts via 
bi-directional long short-term memory (BIDLSTM). Int J Web-
Based Learn Teach Technol. 2021;16(6):1–18.
 10. Bhimavarapu U. IRF-LSTM: enhanced regularization func-
tion in LSTM to predict the rainfall. Neural Comput Appl. 
2022;34(22):20165–77.
 11. Majji R, Prakash PGO, Cristin R, Parthasarathy G. Social bat 
Optimisation dependent deep stacked auto-encoder for skin cancer 
detection. IET Image Process. 2020;14(16):4122–31.
 12. Brahmane AV, Krishna CB. Rider chaotic biography optimization-
driven deep stacked auto-encoder for big data classification using 
spark architecture: Rider chaotic biography optimization. Int J 
Web Serv Res. 2021;18(3):42–62.
 13. Panneerselvam IR. Transfer learning autoencoder used for 
compressing multimodal biosignal. Multimedia Tools Appl. 
2022;81(13):17547–65.
 14. Mahanty M, Bhattacharyya D, Midhunchakkaravarthy D. SRGAN 
assisted encoder-decoder deep neural network for colorectal 
polyp semantic segmentation. Revue d’Intelligence Artificielle. 
2021;35(5):395–401.
 15. Tilak VG, Ghali VS, Kumar AD, Sankar KBS, Sharanya VSNS. 
Deep autoencoder for automatic defect detection in thermal wave 
imaging. J Green Eng. 2020;10(12):13107–18.
 16. Kumar YP, Babu BV. Stabbing of intrusion with learning frame-
work using auto encoder based intellectual enhanced linear sup-
port vector machine for feature dimensionality reduction. Revue 
d’Intelligence Artificielle. 2022;36(5):737–43.
 17. Brahmane AV, Krishna BC. DSAE-deep stack auto encoder and 
RCBO-rider chaotic biogeography optimization algorithm for big 
data classification. Adv Parallel Comput. 2021;39:213–27.
 18. Appathurai A, Sundarasekar R, Raja C, Alex EJ, Palagan CA, 
Nithya A. An efficient optimal neural network-based moving vehi-
cle detection in traffic video surveillance system. Circuits Syst 
Signal Process. 2020;39(2):734–56.
 19. Raju K, et al. A robust and accurate video watermarking system 
based on SVD hybridation for performance assessment. Int J Eng 
Trends Technol. 2020;68(7):19–24. 20. Shaik AA, Mareedu VDP, Polurie VVK. Learning multiview deep 
features from skeletal sign language videos for recognition. Turk 
J Electr Eng Comput Sci. 2021;29(2):1061–76.
 21. Suneetha M, et al. Multi-view motion modelled deep attention 
networks (M2DA-Net) for video-based sign language recognition. 
J Vis Commun Image Represent. 2021;78:103161.
 22. Ghuge CA, Chandra Prakash V, Ruikar SD. Weighed query-spe-
cific distance and hybrid NARX neural network for video object 
retrieval. Comput J. 2020;63(7):1738–55.
 23. Mohan KK, Prasad CR, Kishore PVV. Yolo V2 with bifold 
skip: a deep learning model for video based real time train bogie 
part identification and defect detection. J Eng Sci Technol. 
2021;16(3):2166–90.
 24. Kotkar VA, Sucharita V. Scalable anomaly detection framework in 
video surveillance using keyframe extraction and machine learn-
ing algorithms. J Adv Res Dyn Control Syst. 2020;12(7):395–408.
 25. Suneetha M, Prasad MVD, Kishore PVV. Sharable and unshare-
able within class multi view deep metric latent feature learning for 
video-based sign language recognition. Multimedia Tools Appl. 
2022;81(19):27247–73.
 26. Ali SKA, Prasad MVD, Kumar PP, Kishore PVV. Deep multi 
view spatio temporal spectral feature embedding on skeletal sign 
language videos for recognition. Int J Adv Comput Sci Appl. 
2022;13(4):810–9.
 27. Gullapelly A, Banik BG. Exploring the techniques for object 
detection, classification, and tracking in video surveillance for 
crowd analysis. Indian J Comput Sci Eng. 2020;11(4):321–6.
 28. Ghuge CA, Prakash VC, Ruikar SD. Systematic analysis and 
review of video object retrieval techniques. Control Cybern. 
2020;49(4):471–98.
 29. Priyadharshini B, Gomathi T. Navie bayes classifier for wireless 
capsule endoscopy video to detect bleeding frames. Int J Sci Tech-
nol Res. 2020;9(1):3286–91.
 30. Ali SA, Prasad MVD, Kishore PVV. Ranked multi-view skeletal 
video-BASED sign language recognition with triplet loss embed-
dings. J Eng Sci Technol. 2022;17(6):4367–97.
 31. Krishnamohan K, Prasad CR, Kishore PVV. Train rolling stock 
video segmentation and classification for bogie part inspection 
automation: a deep learning approach. J Eng Appl Sci. 2022. 
https:// doi. org/ 10. 1186/ s44147- 022- 00128-x.
 32. Li X, Manivannan P, Anand M. Task modelling of sports event for 
personalized video streaming data in augmentative and alterna-
tive communication. J Interconnect Netw. 2022. https:// doi. org/  
10. 1142/ S0219 26592 14102 79.
 33. Wagdarikar AMU, Senapati RK. A secure communica-
tion approach in OFDM using optimized interesting region-
based video watermarking. Int J Pervasive Comput Commun. 
2022;18(2):171–94.
 34. Ghuge C, Prakash VC, Ruikar S. An integrated approach using 
optimized naive bayes classifier and optical flow orientation for 
video object retrieval. Int J Intell Eng Syst. 2021;14(3):210–21.
 35. Jadhav AD, Pellakuri V. Highly accurate and efficient two phase-
intrusion detection system (TP-IDS) using distributed processing 
of HADOOP and machine learning techniques. J Big Data. 2021. 
https:// doi. org/ 10. 1186/ s40537- 021- 00521-y .
 36. Adam A, Rivlin E, Shimshoni I, Reinitz D. Robust real-time unu-
sual event detection using multiple fixed-location monitors. IEEE 
Trans Pattern Anal Mach Intell. 2008;30(3):555–60.
 37. Hasan M, Choi J, Neumann J, Roy-Chowdhury AK, Davis LS. 
Learning temporal regularity in video sequences. In: 2016 IEEE 
conference on computer vision and pattern recognition (CVPR). 
2016. pp. 733–742.
 38. Lu C, Shi J, Jia J. Abnormal event detection at 150 fps in Matlab. 
In: 2013 IEEE international conference on computer vision. 2013. 
pp. 2720–2727.
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
 SN Computer Science           (2024) 5:190 
   190  Page 12 of 13
SN Computer Science
 39. Mahadevan V, Li W, Bhalodia V, Vasconcelos N. Anomaly detec-
tion in crowded scenes. In: Proceedings of the IEEE conference 
on computer vision and pattern recognition (CVPR). 2010. pp. 
1975–1981.
 40. Mehran R, Oyama A, Shah M. Abnormal crowd behavior detec-
tion using social force model. In: 2009 IEEE computer society 
conference on computer vision and pattern recognition work -
shops, CVPR workshops 2009. 2009. pp. 935–942.
 41. Patraucean V, Handa A, Cipolla R. Spatio-temporal video autoen-
coder with differentiable memory. In: International conference on 
learning representations, 2015. 2016. pp. 1–10.
 42. Sabokrou M, Fathy M, Hoseini M, Klette R. Real-time anomaly 
detection and localization in crowded scenes. In: 2015 IEEE con-
ference on computer vision and pattern recognition workshops 
(CVPRW). 2015. pp. 56–62.
 43. Shi X, Chen Z, Wang H, Yeung DY, Wong W, Woo W. Convo -
lutional LSTM network: a machine learning approach for pre-
cipitation nowcasting. In: Proceedings of the 28th international 
conference on neural information processing systems, NIPS 2015. 
Cambridge, MA, USA: MIT Press; 2015. pp. 802–810.
 44. Wang T, Snoussi H. Histograms of optical flow orientation for 
abnormal events detection. In: IEEE international workshop on 
performance evaluation of tracking and surveillance, PETS. 2013. 
pp. 45–52.
 45. Yen SH, Wang CH. Abnormal event detection using HOSF. In: 
2013 International conference on IT convergence and security, 
ICITCS 2013. 2013.
 46. Zhao B, Fei-Fei L, Xing EP. Online detection of unusual events 
in videos via dynamic sparse coding. In: Proceedings of the IEEE 
computer society conference on computer vision and pattern rec-
ognition. 2011. pp. 3313–3320
 47. Zhou S, Shen W, Zeng D, Fang M, Wei Y, Zhang Z. Spatial-
temporal convolutional neural networks for anomaly detection 
and localization in crowded scenes. Sig Process Image Commun. 
2016;47:358–68.
 48. Shakeela S, Shankar NS, Reddy PM, Tulasi TK, Koneru MM. 
Optimal ensemble learning based on distinctive feature selection 
by univariate ANOVA-F statistics for IDS. Int J Electron Telecom-
mun. 2021;67(2):267–75. 49. Jadhav AD, Pellakuri V. Accuracy based fault tolerant two phase—
intrusion detection system (TP-IDS) using machine learning and 
HDFS. Revue d’Intelligence Artificielle. 2021;35(5):359–66.
 50. Hira S, Bai A, Hira S. An automatic approach based on CNN 
architecture to detect Covid-19 disease from chest X-ray images. 
Appl Intell. 2021;51(5):2864–89.
 51. Murthy MYB, Koteswararao A, Babu MS. Adaptive fuzzy 
deformable fusion and optimized CNN with ensemble classifi-
cation for automated brain tumor diagnosis. Biomed Eng Lett. 
2022;12(1):37–58.
 52. Kumar S, Jain A, Rani S, Alshazly H, Idris SA, Bourouis S. Deep 
neural network based vehicle detection and classification of aerial 
images. Intelligent Autom Soft Comput. 2022;34(1):119–31.
 53. Lakshmi Mallika I, Venkata Ratnam D, Raman S, Sivavaraprasad 
G. A new ionospheric model for single frequency GNSS user 
applications using Klobuchar model driven by auto regressive 
moving average (SAKARMA) method over Indian region. IEEE 
Access. 2020;8:54535–53.
 54. Thirugnanasambandam K, Rajeswari M, Bhattacharyya D, Kim 
J-Y. Directed artificial bee colony algorithm with revamped search 
strategy to solve global numerical optimization problems. Autom 
Softw Eng. 2022. https:// doi. org/ 10. 1007/ s10515- 021- 00306-w .
 55. Sasank VVS, Venkateswarlu S. An automatic tumour growth 
prediction-based segmentation using full resolution convolu-
tional network for brain tumour. Biomed Signal Process Control. 
2022;71:103090.
 56. Budati AK, Katta RB. An automated brain tumor detection and 
classification from MRI images using machine learning tech-
niques with IoT. Environ Dev Sustain. 2022;24(9):10570–84.
 57. Gopi Tilak V, Ghali VS, Vijaya Lakshmi A, Suresh B, Naik RB. 
Proximity based automatic defect detection in quadratic fre-
quency modulated thermal wave imaging. Infrared Phys Technol. 
2021;114:103674.
 58. Bhimanpallewar RN, Narasingarao MR. AgriRobot: Implementa-
tion and evaluation of an automatic robot for seeding and fertiliser 
microdosing in precision agriculture. Int J Agric Resour Gov Ecol. 
2020;16(1):33–50.
 59. Thamizhazhagan P, et al. AI based traffic flow prediction model 
for connected and autonomous electric vehicles. Comput Mater 
Contin. 2022;70(2):3333–47.
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
SN Computer Science           (2024) 5:190  
 Page 13 of 13   190 
SN Computer Science
 60. Vesala GT, Ghali VS, Lakshmi AV, Naik RB. Deep and hand-
crafted feature fusion for automatic defect detection in quadratic 
frequency modulated thermal wave imaging. Russ J Nondestr 
Test. 2021;57(6):476–85.
 61. Vijayalakshmi A, Ghali VS, Chandrasekhar Yadav GVP, Gopitilak 
V, Muzammil Parvez M. Machine learning based automatic defect 
detection in non-stationary thermal wave imaging. ARPN J Eng 
Appl Sci. 2020;15(2):172–8.Publisher's Note Springer Nature remains neutral with regard to 
jurisdictional claims in published maps and institutional affiliations.
Springer Nature or its licensor (e.g. a society or other partner) holds 
exclusive rights to this article under a publishing agreement with the 
author(s) or other rightsholder(s); author self-archiving of the accepted 
manuscript version of this article is solely governed by the terms of 
such publishing agreement and applicable law.
Authors and Affiliations
Ghayth Almahadin1 · Maheswari Subburaj2  · Mohammad Hiari3 · Saranya Sathasivam Singaram4 · 
Bhanu Prakash Kolla5 · Pankaj Dadheech6 · Amol D. Vibhute7 · Sudhakar Sengan8
 * Maheswari Subburaj 
 <EMAIL>
 * Sudhakar Sengan 
 <EMAIL>
 Ghayth Almahadin 
 <EMAIL>
 Mohammad Hiari 
 <EMAIL>
 Saranya Sathasivam Singaram 
 <EMAIL>
 Bhanu Prakash Kolla 
 <EMAIL>
 Pankaj Dadheech 
 <EMAIL>
 Amol D. Vibhute 
 <EMAIL>
1 Department of Networks and Cybersecurity, Faculty 
of Information Technology, Al Ahliyya Amman University 
Country, Amman, Jordan2 School of Computer Science and Engineering, Vellore 
Institute of Technology, Chennai 600127, Tamil Nadu, India
3 Department of Networks and Cybersecurity, Information 
Technology, Al Ahliyya Amman University, Amman, Jordan
4 Department Computing Technology, School of Computing, 
SRM Institute of Science and Technology, Kattankulathur 
Campus, Chennai 603203, Tamil Nadu, India
5 Department of Computer Science and Engineering, 
Koneru Lakshmaiah Education Foundation, Vaddeswaram, 
Guntur 522302, India
6 Department of Computer Science and Engineering, 
Swami Keshvanand Institute of Technology, Management 
and Gramothan (SKIT), Jaipur 302017, Rajasthan, India
7 Symbiosis Institute of Computer Studies and Research 
(SICSR), Symbiosis International (Deemed University), 
Pune 411016, MH, India
8 Department of Computer Science and Engineering, PSN 
College of Engineering and Technology, Tirunelveli 627152, 
Tamil Nadu, India
Content courtesy of Springer Nature, terms of use apply. Rights reserved.
1.
2.
3.
4.
5.
6.Terms and Conditions 
Springer Nature journal content, brought to you courtesy of Springer Nature Customer Service Center GmbH (“Springer Nature”). 
Springer Nature supports a reasonable amount of sharing of  research papers by authors, subscribers and authorised users (“Users”), for small-
scale personal, non-commercial use provided that all copyright, trade and service marks and other proprietary notices are maintained. By
accessing, sharing, receiving or otherwise using the Springer Nature journal content you agree to these terms of use (“Terms”). For these
purposes, Springer Nature considers academic use (by researchers and students) to be non-commercial. 
These Terms are supplementary and will apply in addition to any applicable website terms and conditions, a relevant site licence or a personal
subscription. These Terms will prevail over any conflict or ambiguity with regards to the relevant terms, a site licence or a personal subscription
(to the extent of the conflict or ambiguity only). For Creative Commons-licensed articles, the terms of the Creative Commons license used will
apply. 
We collect and use personal data to provide access to the Springer Nature journal content. We may also use these personal data internally within
ResearchGate and Springer Nature and as agreed share it, in an anonymised way, for purposes of tracking, analysis and reporting. We will not
otherwise disclose your personal data outside the ResearchGate or the Springer Nature group of companies unless we have your permission as
detailed in the Privacy Policy. 
While Users may use the Springer Nature journal content for small scale, personal non-commercial use, it is important to note that Users may
not:  
use such content for the purpose of providing other users with access on a regular or large scale basis or as a means to circumvent access
control;
use such content where to do so would be considered a criminal or statutory offence in any jurisdiction, or gives rise to civil liability, or is
otherwise unlawful;
falsely or misleadingly imply or suggest endorsement, approval , sponsorship, or association unless explicitly agreed to by Springer Nature in
writing;
use bots or other automated methods to access the content or redirect messages
override any security feature or exclusionary protocol; or
share the content in order to create substitute for Springer Nature products or services or a systematic database of Springer Nature journal
content. 
In line with the restriction against commercial use, Springer Nature does not permit the creation of a product or service that creates revenue,
royalties, rent or income from our content or its inclusion as part of a paid for service or for other commercial gain. Springer Nature journal
content cannot be used for inter-library loans and librarians may not upload Springer Nature journal content on a large scale into their, or any
other, institutional repository. 
These terms of use are reviewed regularly and may be amended at any time. Springer Nature is not obligated to publish any information or
content on this website and may remove it or features or functionality at our sole discretion, at any time with or without notice. Springer Nature
may revoke this licence to you at any time and remove access to any copies of the Springer Nature journal content which have been saved. 
To the fullest extent permitted by law, Springer Nature makes no warranties, representations or guarantees to Users, either express or implied
with respect to the Springer nature journal content and all parties disclaim and waive any implied warranties or warranties imposed by law,
including merchantability or fitness for any particular purpose. 
Please note that these rights do not automatically extend to content, data or other material published by Springer Nature that may be licensed
from third parties. 
If you would like to use or distribute our Springer Nature journal content to a wider audience or on a regular basis or in any other manner not
expressly permitted by these Terms, please contact Springer Nature at  
<EMAIL> 
