METADATA:
------------------------------
Title: Generalized Video Anomaly Event Detection: Systematic Taxonomy and Comparison of Deep Models
Author: 
Subject: ACM Comput. Surv. 2024.56:1-38
Creator: LaTeX with hyperref package
Producer: Acrobat Distiller 11.0 (Windows); modified using iText 4.2.0 by 1T3XT
CreationDate: D:20240406093615+05'30'
Pages: 38

FULL TEXT:
------------------------------
Generalized Video Anomaly Event Detection: Systematic
Taxonomy and Comparison of Deep Models
YANG LIU ,FudanUniversity,Shanghai, ChinaandUniversityof Toronto,Toronto,Canada
DINGKANG YANG andYAN WANG ,Fudan University,Shanghai, China
JINGLIU,FudanUniversity,Shanghai,China,UniversityofBritishColumbia,Vancouver,Canada,andDuke
KunshanUniversity,Suzhou, China
JUNLIU,Singapore Universityof Technology and Design,Singapore, Singapore
AZZEDINE BOUKERCHE ,Universityof Ottawa, Ottawa, Canada
PENG SUN ,DukeKunshan University,Kunshan,China
LIANG SONG ,Fudan University,Shanghai, China
Video Anomaly Detection (VAD) serves as a pivotal technology in the intelligent surveillance systems,
enabling the temporal or spatial identification of anomalous events within videos. While existing reviews
predominantly concentrate on conventional unsupervised methods, they often overlook the emergence
of weakly-supervised and fully-unsupervised approaches. To address this gap, this survey extends the
conventional scope of VAD beyond unsupervised methods, encompassing a broader spectrum termed
Generalized Video Anomaly Event Detection (GVAED). By skillfully incorporating recent advancements
rooted in diverse assumptions and learning frameworks, this survey introduces an intuitive taxonomy
This paper was revised by Yang Liu during his FDU-UofT Joint Ph.D. Training Program at the University of Toronto,
Canada.
This work is supported in part by the China Mobile Research Fund of the Chinese Ministry of Education under Grant No.
KEH2310029, the National Natural Science Foundation of China under Grant No. 62250410368, and the Specific Research
FundoftheInnovationPlatformforAcademiciansofHainanProvinceunderGrantNo.YSPTZX202314.Additionalsupport
is acknowledged from the Shanghai Key Research Laboratory of NSAI and the Joint Laboratory on Networked AI Edge
ComputingFudanUniversity-Changan.TheworkofYangLiuwasfinanciallysupportedinpartbytheChinaScholarship
Council (File No. 202306100221). The authors extend their appreciation to the anonymous reviewers for their valuable
commentsandsuggestions, aswellas tothe authors of thereviewedpapersfor their contributions to thefield.
Authors’ addresses: Y. Liu, Academy for Engineering & Technology, Fudan University, No. 220 Handan Road, Yangpu
District, Shanghai, 200433, China and Department of Computer Science, University of Toronto, 27 King’s College Cir,
Toronto, M5S 1A1, Ontario, Canada; e-mail: <EMAIL>; D. Yang, Y. Wang, and L. Song (Corresponding
author), Academy for Engineering & Technology, Fudan University, No. 220 Handan Road, Yangpu District, Shanghai,
200433, China; e-mails: <EMAIL>, <EMAIL>, <EMAIL>; J. Liu, School of Infor-
mation Science and Technology, Fudan University, No. 220 Handan Road, Yangpu District, Shanghai, 200433, China, and
DepartmentofElectricalandComputerEngineering,UniversityofBritishColumbia,2329WestMall,Vancouver,V6T1Z4,
British Columbia, Canada and Division of Natural and Applied Sciences, Duke Kunshan University, No. 8 Duke Avenue,
Kunshan, Suzhou, 215316, Jiangsu, China; e-mail: <EMAIL>; J. Liu, Information Systems Technology and
Design(ISTD)Pillar,SingaporeUniversityofTechnologyandDesign,8SomapahRd,Singapore,487372,Singapore;e-mail:
<EMAIL>; A. Boukerche, School of Information Technology and Engineering, University of Ottawa, 75 Laurier
AveE,Ottawa,K1N6N5,Ontario,Canada;e-mail:<EMAIL>;P.Sun(Correspondingauthor),DivisionofNatu-
ralandAppliedSciences,DukeKunshanUniversity,No.8DukeAvenue,Kunshan,Suzhou,215316,Jiangsu,China;e-mail:
<EMAIL>.
Permission to make digital or hard copies of all or part of this work for personal or classroom use is granted without fee
provided that copies are not made or distributed for profit or commercial advantage and that copies bear this notice and
the full citation on the first page. Copyrights for components of this work owned by others than the author(s) must be
honored.Abstractingwithcreditispermitted.Tocopyotherwise,orrepublish,topostonserversortoredistributetolists,
requires prior specific permission and/or a fee.Request <NAME_EMAIL> .
© 2024Copyrightheld bytheowner/author(s). Publicationrights licensed toACM.
ACM 0360-0300/2024/04-ART189
https://doi.org/10.1145/3645101
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.

189:2 Y. Liu et al.
that seamlessly navigates through unsupervised, weakly-supervised, supervised and fully-unsupervised
VAD methodologies, elucidating the distinctions and interconnections within these research trajectories. In
addition, this survey facilitates prospective researchers by assembling a compilation of research resources,
including public datasets, available codebases, programming tools, and pertinent literature. Furthermore,
this survey quantitatively assesses model performance, delves into research challenges and directions, and
outlines potentialavenues forfutureexploration.
CCSConcepts:• Generalandreference →Surveysandoverviews ;•Appliedcomputing →Surveillance
mechanisms ;•Informationsystems →Data streaming;
AdditionalKeyWordsandPhrases:Anomalydetection,videounderstanding,deeplearning,intelligentsurvil-
lance system
ACM Reference Format:
YangLiu,DingkangYang,YanWang,JingLiu,JunLiu,AzzedineBoukerche,PengSun,andLiangSong.2024.
GeneralizedVideoAnomalyEventDetection:SystematicTaxonomyandComparisonofDeepModels. ACM
Comput.Surv. 56, 7,Article 189 (April 2024), 38pages. https://doi.org/10.1145/3645101
1 INTRODUCTION
Surveillance cameras can sense environmental spatial-temporal information without contact and
have been the primary data collection tool for public services such as security protection [ 28],
crimewarning[ 158],andtrafficmanagement[ 101].However,withtherapiddevelopmentofsmart
cities and digital society, the number of surveillance cameras is growing explosively, making the
ensuing video analysis a significant challenge. Traditional manual inspection is time-consuming
andlaboriousandmaycausemissingdetectionsduetohumanvisualfatigue[ 100],hardlycoping
withthevastscalevideostream.Asthecoretechnologyofintelligentsurveillancesystems, Video
Anomaly Detection (VAD) aims to automatically analyze video patterns and locate abnormal
events. Due to its potential application in unmanned factories, self-driving vehicles, and secure
communities,VADhasreceivedwide attentionfrom academiaand industry.
VADinanarrowsenserefersspecificallytothe unsupervised researchparadigmthatusesonly
normal videos to learn a normality model, abbreviated as UVAD. Such methods share the same
assumptionasthelong-established AnomalyDetection(AD) tasksinnon-visualdata(e.g.,time
series[7]andgraphs[ 117])andimages[ 61].Theyassumethenormalitymodellearnedonnormal
samplescannotrepresentanomaloussamples.Typically,UVADconsistsoftwophases,normality
learning and downstream anomaly detection [ 50,92,101,197]. UAVD shares a similar modeling
process with other AD tasks without predefining and collecting anomalies, following the open-
world principle. In the real world, anomalies are diverse and rare, so they cannot be defined and
fullycollectedinadvance.Therefore,UVADwasfavoredbyearlyresearchersandwasonceconsid-
ered the prevailing VAD paradigm. However, the definition of anomaly is idealistic, ignoring that
normaleventsarediverse.Itisalsounrealistictocollectallpossibleregulareventsformodeling.In
addition,thelearnedUVADmodelhasdifficultymaintainingareasonablebalancebetweenrepre-
sentationandgeneralizationpower,eitherduetotheinsufficientrepresentationalthatfalse-alarms
unseen normal events as anomalies or the excessive generalization power that effectively recon-
structs anomalous events. Numerous experiments [ 218] have shown that UVAD is valid for only
simplescenarios.Themodelperformanceoncomplexdatasets[ 92]ismuchmoretothatofsimple
single-scenevideos[ 84,107],whichlimitstheapplicationofVADtechnicalsinrealisticscenarios.
In contrast, Weakly-supervised Abnormal Event Detection (WAED) departs from the
ambiguoussettingthatallareanomalousexceptnormalwithaclearerdefinitionfortheanomaly
that is more consistent with human consciousness (e.g., traffic accidents, robbery, stealing, and
shooting)[ 158].Givenitspotentialforimmediatereferencesinreal-lifeapplicationssuchastraffic
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:3
Fig. 1. Taxonomy of Generalized Video Anomaly Event Detection (GVAED). We provide a hierarchical
taxonomy that organizes existing deep GVAED models by supervised signals, model inputs, and network
structure into a systematic framework, including Unsupervised Video Anomaly Detection (UVAD), Weakly-
supervised Abnormal Event Detection (WAED), Fully-unsupervised VAD (FVAD) and Supervised VAD
(SVAD). Besides, we collate benchmark datasets, evaluation metrics, available codes, and literature to a
public GitHub repository1.Finally, we analyze theresearch challenges and possibletrends.
management platforms and violence warning systems, WAED has become another mainstream
VAD paradigm since 2018 [ 39,98,164]. Generally, WAED models directly output anomaly scores
by comparing the spatial-temporal features of normal and abnormal events through Multiple
Instance Learning (MIL) . The previous study [ 39] proved that WAED could understand the
essential difference between normal and abnormal. Therefore, its results are more reliable than
thatofUVAD.Unfortunately,WAEDdoesnotfollowthebasicassumptionsofADtasks,whichis
more like a binary classification under unique settings (e.g., data imbalance and positive samples
containing multiple subcategories). Therefore, existing reviews [ 12,136,139]m a i n l yf o c u so n
UVAD and consider WAED as a marginal research pathway, lacking the systematic organization
for WAEDdatasetsandmethods.
In recent times, certain researchers [ 129,207] have introduced fully-unsupervised methods,
which eliminate the need for labels and eliminate any prerequisites on the training data. Given
the deeply ingrained association of UVAD with modeling using solely normal data, we retain
the nomenclature UVAD for these techniques, rather than referring to them as semi-supervised
VAD. Conversely, the emerging paradigm of absolute unsupervised setting is denoted as FVAD
(Fully-unsupervised VAD) , contributing to terminology conceptual clarity in this survey and
futureresearch.
Insummary,thissurveyfocusesonanomalyeventdetectioninsurveillancevideos,integrating
deep VAD methods based on different assumptions, learning paradigms, and supervision into a
systematic taxonomy: Generalized Video Anomaly Event Detection (GVAED) ,a ss h o w ni n
Figure1. We compare the differences and performance among different methods, sorting out the
recent advances in GVAED. In addition, we collate available research resources, such as datasets,
metrics,codes,andliterature,intoapublicGitHubrepository1.Moreover,weanalyzetheresearch
challengesandfuturetrends,whichcanguidefurtherresearchandpromotethedevelopmentand
applicationsof GVAED.
1https://github.com/fudanyliu/GVAED.git
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:4 Y. Liu et al.
Fig.2. Publicationandcitationstatisticsonthetopicof(a) VideoAnomalyDetection and(b)AbnormalEvent
Detection .
1.1 Literature Statistics
Wecountthepublicationsandcitationsofacademicpapersonthetopicof VideoAnomalyDetection
andAbnormalEventDetection inthepast12yearsthroughreferencedatabases(e.g.,ACMDigital
Library,IEEEexplore,ScienceDirect,andSpringerLink)andsearchengines.Theresultsareshown
inFigure 2,wherethebarandlinegraphindicatethenumberofpublicationsandcitations,respec-
tively. The lines in Figure 2(a) and Figure 2(b) show a steadily increasing trend, indicating that
GVAEDhasreceivedwideattention.Therefore,asystematictaxonomyandcomparisonofGVAED
methodsarenecessarytoguidefurtherdevelopment.Asmentionedabove,currentworksfocuson
unsupervised methods that use only regular videos to train models to represent normality. Thus,
the development of UVAD is limited by representation means. Until 2016, UVAD utilized hand-
icraft features, such as Local Binary Patterns (LBP) [56,123,216],Histogram Of Gradients
(HOG)[50,116,148],andSpace-TimeInterestPoint(STIP) [34].Theperformanceispoorand
relies on ap r i o r iknowledge. As a result, VAD developed slowly. Fortunately, after 2016, with the
development of deep learning, especially the application of Convolutional Neural Networks
(CNNs)in image processing [ 20,74,193] and video understanding [ 194–196], VAD has ushered
in new development opportunities. The research progress increased significantly, as shown in
Figure2(a). Deep CNNs can extract the video patterns end-to-end, freeing VAD research from
complexaprioriknowledgeconstruction.Inaddition,comparedwithmanualfeatures[ 34,56,148],
deep representations can capture multi-scale spatial semantic features and extract long-range
temporal contextual features, which are more efficient in learning video normality. On the one
hand,thelargeamountofvideogeneratedbythesurveillancecamerasprovidessufficienttraining
data for deep GVAED models. On the other hand, the iteratively updated Graphics Processing
Units(GPUs) makeitpossibletotrainlarge-scalemodels.Asaresult,VADhasdevelopedrapidly
inrecentyearsandstartedtomovefromacademicresearchtocommercialapplications.Similarly,
Figure2(b) reflects the research enthusiasm and development potential of abnormal event detec-
tion. To accelerate the application of GVAED in terminal devices and inspire future researchers,
this survey organizes various GVAED models into a unified framework. Additionally, we collect
commonly useddatasets,publiclyavailable codes,and classicliteraturefor furtherresearch.
1.2 Related Reviews
In the past four years, several reviews [ 7,12,26,64,67,106,122,125,128,136,139,144,150,154]
havecoveredGVAEDworksandgeneratedvariousclassificationsystems.Weanalyzethemethod-
ologies covered in recent reviews and the research topics related to real-world deployment, as
showninTable 1.Themainstreamreviews[ 67,139]stillconsiderVADasanarrowunsupervised
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:5
Table 1. AnalysisandComparison of RelatedReviews
Year Ref. Main FocusResearchPathwaysaTopicsa,b
UVAD WAED SVAD FVAD LW OD CS OE
2018 [67] Unsupervised andsemi-supervised methods. ✓✗✗ ✗ ✓✗✗✗
2019 [106] Weakly-supervisedVADmethods andapplications. ✗✓✗ ✗ ✗✗ ✗ ✗
2020 [139] Unsupervised single-scenevideoanomalydetection. ✓✗✓ ✗ ✓✗ ✓ ✗
2021 [125] Deeplearningdriven unsupervised VADmethods. ✓✗✗ ✗ ✓✗✗✗
2021 [144] Unsupervised crowdanomalydetectionmethods. ✓✗✗ ✗ ✗✗ ✗ ✗
2021 [150] Trafficscenevideoanomalydetection. ✓✗✗ ✗ ✗✗ ✗ ✗
2022 [136] Unsupervised videoanomalydetection. ✓✗✗ ✗ ✗✓ ✗✗
2022 [12] One&two-classclassification-basedmethods. ✓✓✓ ✗ ✓✓✗✗
2023 Ours GVAED taxonomy,challengesandtrends. ✓✓✓ ✓ ✓✓ ✓ ✓
a:✗meansno systematicanalysis,while ✓isviceversa. b:LW=lightweight,OD=onlinedetection,CS=cross-scene, and
OE=onlineevolution.
task, lacking attention to WAED with excellent application value and FVAD methods using un-
limited training data. In addition, they are biased against Supervised Video Anomaly Detec-
tion(SVAD) ,arguingthatdatalabelingmakesSVADchallengingtodevelop.However,thegame
engines[38,152]andautomaticannotations[ 42,49]makeitpossibletoobtainanomalousevents
and fine-grained labels. In addition, the existing review suffers from three major weaknesses: (1)
[139] and [150] attempted to link existing works to specific scenes, missing the cross-scene chal-
lengesinreal-world.Specifically,[ 139]pointedoutthatexistingworksweretrainedandtestedon
videosofthesamescene,sotheyonlyreviewedsingle-singlemethods,leavingoutthelatestcross-
sceneVADresearch.[ 150]focusesonthetrafficVADmethods,innovativelyanalyzingtheapplica-
bility of existing works in traffic scenes. However, weakly-supervised methods for crime and vio-
lencedetectionfailtobeincludedin[ 150].(2)Duetotimeliness,earlierreviews[ 26,125,144,150]
wereunabletocoverthelatestresearchandwereoutdatedforpredictingresearchtrends.Recent
surveys[12,136]lackdiscussionoftheinteractionofGVAEDwithnewtechniquessuchascausal
machinelearning[ 88,103],domainadaptation[ 44,183],andonlineevolutivelearning[ 76,77,156],
which are expected to be the future directions of GVAED and essential to model deployment. (3)
Although the latest review [ 12] in 2022 has started to incorporate WAED and SVAD, it still treats
them as a marginal exploration, lacking a systematic organization of the datasets, literature, and
trends.
1.3 Contribution Summary
GVAED will usher in new development opportunities with the rapid growth of deep learning
technicals and surveillance videos. To clarify the development of GVAED and inspire future
research, this survey integrates UVAD, WAED, FVAD, and SVAD into a unified framework from
anapplicationperspective.Themaincontributionsofthissurveyaresummarizedinthefollowing
four aspects:
(1) Tothebestofourknowledge,itisthefirstcomprehensivesurveythatextendsvideoanomaly
detectionfromnarrowunsupervisedmethodstogeneralizedvideoanomalyeventdetection.
We analyze the various research routes and clearly state the lineage and trends of deep
GVAED models tohelpadvancethefield.
(2) WeorganizevariousGVAEDmethodswithdifferentassumptionsandlearningframeworks
from an application perspective, providing an intuitive taxonomy based on supervision
signals, inputdata,and networkstructures.
(3) This survey collects accessible datasets, literature, and codes and makes them publicly
available.Moreover,weanalyzethepotentialapplicationsofotherdeeplearningtechniques
and structuresinGVAED tasks.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:6 Y. Liu et al.
(4) We examine the research challenges and trends within GVAED in the context of the
developmentofdeeplearningtechniques,whichisanticipatedtoprovidevaluableguidance
forupcomingresearchersand engineers.
The remainder of this survey is organized as follows. Section 2provides an overview of the
basics and research background of GVAED, including the definition of anomalies, basic assump-
tions,mainevaluationmetrics,andbenchmarkdatasets.Sections 3–5introducetheunsupervised,
weakly-supervised,supervisedandfully-unsupervisedGVAEDmethods,respectively.Weanalyze
the extant methods’ general ideas and specific implementations and compare their strengths and
weaknesses. Further, we quantitatively compare the performance in Section 7. Section8analyzes
the challenges and research outlook on the development of GVAED. Section 9concludes this
survey.
2 FOUNDATIONS OF GVAED
2.1 Definition of the Anomaly
UVAD follows the assumption of general AD tasks [ 128] and considers all events that have not
occurred in the training set as abnormal. In other words, the training set of the UVAD dataset
contains only normal events, while videos in the test set that differ from the training set are
considered anomalies. Thus, certain normal events in the subjective human consciousness may
also be labeled anomalies. For instance, in the UCSD Pedestrian datasets [ 84], riding a bicycle
on the college campus is labeled abnormal simply because the training set fails to contain
such events. This seemingly odd definition is dictated by the diversity and rarity of real-world
anomalies. Collecting a sufficient number of anomalous events with a full range of categories
is nearly impossible. In response, researchers have taken the alternative route of collecting
enough normal videos to train models to describe the boundary of normal patterns and treat
events that fall outside the boundary as anomalies. Unfortunately, it is also costly to collect all
possible normal events for training. In addition, abnormal and normal frames share most of the
appearance and motion information, making their patterns overlap. Therefore, letting the model
finda discriminativepatternboundarywithoutseeingabnormaleventsis infeasible.
In contrast, WAED takes a more intuitive definition of anomalies. Events that are subjectively
perceived as abnormal by humans are considered anomalies, such as thefts and traffic accidents
[158]. The training set for WAED tasks contains both normal and abnormal events and provides
easilyaccessiblevideo-levellabelstosupervisethemodel.Comparedwithfine-grainedframe-level
labels, video-level labels only tell the model whether a video contains abnormal events without
revealingtheexactlocationoftheabnormalities,avoidingthecostlyframe-by-framelabelingand
providing more reliable supervision. In contrast, the discrete frame-level annotations (0=normal,
1=abnormal) in SVAD ignore the transition continuity from normal to abnormal events. WAED
needsto predefineabnormaleventssothatit canonly distinguishspecifiedabnormalevents.
2.2 Problem Formation
InUVAD,thetrainingdatacontainsonlynormalevents,asshowninFigure 3(a).Suchmethodsaim
todescribetheboundariesofnormalspatial-temporalpatternswithaproxytaskandconsiderthe
testsampleswhosepatternsfalloutsidethelearnedboundariesasanomalies[ 104].Figure4shows
the two-stage anomaly detection framework in UVAD. The deep network trained by performing
theproxytaskinthetrainingphaseisdirectlyappliedasanormalitymodelforanomalydetection
in the testing phase. The performance of the proxy task is a credential to calculate the anomaly
score.Formulaically,theprocessof UVADisas follows:
e=d(f(xtest;θ),xtest) (1)
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:7
Fig. 3. Illustration of training data. (a) UVAD trains the model using only normal data, with the hidden
implication that all video-level and frame-level labels are 0. (b) WAED models use positive and negative
samplesandrequireframe-levellabels,where Y=0indicatesnormalvideoand Y=1indicatesananomaly.
(c) SVAD is trained using a fine-grained frame-level labeling supervised model, where the semantics of the
frame-levellabelsexposethevideo-levellabels.(d)FVADattemptstolearntheanomalydetectorfromunder-
processed data with training data containing both normal and anomalous samples and without any level
of labeling.
Fig. 4. Illustration of the two-stage UVAD framework. Anomaly detection is performed in the test phase as
a downstream task of proxy task-based normality learning. The example video frames are from the CUHK
Avenue [107]dataset.
whereθdenotes the learnable parameters of the deep model f, designed to characterize the
prototype of normal events. ddenotes the deviation between the test sample xtestand the
well-trained f, which is usually a quantifiable distance, such as the Mean Square Error (MSE)
of the prediction result, the L2distance in the feature space and the difference of the distribution
[112,131,219]. Noting that the normality model is obtained by optimizing the proxy task.
This process is independent of the downstream anomaly detection, so the performance of the
proxy task cannot directly determine the anomaly detection performance. In addition, for the
reconstruction-based [ 46,50] and prediction-based [ 92,101] methods, the final anomaly score is
usually a relative value in the range [0,1]. A higher score indicates a larger deviation. Generally,
these methods convert the absolute deviation einto a relative anomaly score by performing
maximum-minimumnormalization.Theynotonlyexplicitlyrequirealltrainingdatatobenormal
but also include the hidden assumption that the test videos must include anomalous events. In
otherwords,anytestvideowillyieldhighscoreintervals,whichindicatesthatsuchmethodsare
offline and may producefalse alarmsfor normalvideos.
WAED methods [ 39,98,164] always follow the MIL ranking framework. Figure 3(b) shows
the training data composition for WAED, where both normal and anomalous events need to be
pre-collected and labeled. Video-level labels are easy to obtain and often more accurate than the
fine-grained frame-level labels for SVAD shown in Figure 3(c). In a concrete implementation,
WAEDtreatsthevideoasabagcontainingseveralinstances,asillustratedinFigure 5.Thenormal
videoVnformsanegativebag Bn,whiletheabnormalvideo Vaapositivebag Ba.BasedonMIL,
WVEAD aims to train a regression model r(·)to assign scores to instances, with the basic goal
that the maximum score of Bais higher than that of Bn. Thus, the WAED methods do not rely
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:8 Y. Liu et al.
Fig.5. StructureoftheMILrankingmodel[ 158].Theanomalousvideo Vaandthenormalvideo Vnarefirst
slicedintoseveralequal-sizeinstances.Thepositivebag Bacontainsatleastonepositiveinstance,whilethe
negative bag Bncontains only normal instances. In the test phase, the well-trained MIL regression model
outputtheanomaly scores of instances inthetestvideo Vtdirectly.
on an additional self-supervised proxy task but compute anomaly scores directly. The objective
functionO(Ba,Bn)is asfollows:
O(Ba,Bn)=minmax/parenleftbigg
0,1−max
i∈Bar/parenleftbigVi
a/parenrightbig+max
i∈Bnr/parenleftbigVi
n/parenrightbig/parenrightbigg
+λ1Csmooth/bracehtipdownleft/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext /bracehtipupright/bracehtipupleft /bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext /bracehtipdownright
n−1/summationdisplay.1
i/parenleftbigr/parenleftbigVi
a/parenrightbig−r/parenleftbigVi+1
a/parenrightbig/parenrightbig2+λ2Csparsity
/bracehtipdownleft/bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehtipupright/bracehtipupleft /bracehext/bracehext/bracehext/bracehext/bracehext/bracehext/bracehtipdownright
n/summationdisplay.1
ir/parenleftbigVi
a/parenrightbig
(2)
Inadditiontotheadditionalanomalycurvesmoothnessconstraint Csmoothandsparsityconstraint
Csparsity,t h ec o r eo f O(Ba,Bn)is to train a ranking model capable of distinguishing the spatial-
temporal patterns between BaandBn. Subsequent WAED works [ 39,98,102,164,172,223]h a v e
followed the idea of MIL ranking and made effective improvements regarding feature extraction
[223], label denoising [ 220], and the objective function [ 98]. However, as shown in Figure 5,t h e
MIL regression module takes the extracted feature representations as input, so the performance
of WAED methods partially depends on the pre-trained feature extractor, making the calculation
costly.
The training data of FVAD contains both normal and abnormal, and none of the data labels
are available for model training, as shown in Figure 3(d). One class of FVAD methods follows a
similarworkflowtothatofUVAD,i.e.,learningthenormalitymodeldirectlyfromtheoriginaldata.
Althoughthetrainingdatacontainsanomalousevents,thelowfrequencyofanomalieslimitstheir
impactonmodeloptimization.Asaresult,themodellearnedonmanynormalvideosandasmall
numberofabnormalframesisstillonlyeffectiveinrepresentingnormaleventsandgenerateslarge
errors for anomalous events. Another class of methods tries to discover anomalies through the
mutual collaboration of the representation learner and anomaly detector. Generally, the learning
processofFVAD canbeformulated asfollows:
F=argmin
Θ/summationdisplay.1
I∈ILfoc(ˆy=ϕ(m=φ(x=f(I))),l) (3)
where the aim is to learn an anomaly detector Fvia a deep neural network which consists of a
backbonenetwork f(·;Θb):RH×W×3/mapsto→RDbthattransformsaninputvideoframe Itofeature x,
an anomaly representation learner φ(·;Θa):RDb/mapsto→RDnthat converts xto an anomaly specific
representation m,andananomalyscoreregressionlayer ϕ(·;Θs):RDs/mapsto→Rthatlearnstopredict
mtoananomalyscore y.Theoverallparameters Θ={Θb,Θa,Θs}areoptimizedbythefocalloss.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:9
Table 2. RepresentativeGVAEDDatasets
Year Dataset#Videos #Frames#Scenes #Anomalies
Total Training Testing Total Training Testing Normal Abnormal
2008 Subway Entrance2-- - 144,250 76,543 67,797 132,138 12,112 1 51
2008 Subway Exit3-- - 64,901 22,500 42,401 60,410 4,491 1 14
2011 UMN3†-- - 7,741 - - 6,165 1,576 3 11
2013 UCSDPed1470 34 36 14,000 6,800 7,200 9,995 4,005 1 61
2013 UCSDPed2528 16 12 4,560 2,550 2,010 2,924 1,636 1 21
2013 CUHKAvenue537 16 21 30,652 15,328 15,324 26,832 3,820 1 77
2018 ShanghaiTech6-- - 317,398 274,515 42,883 300,308 17,090 13 158
2018UCF-Crime71,900 1,610 290 13,741,393 12,631,211 1,110,182 - - - 950
2019ShanghaiTechWeakly8437 330 107 --- - - - -
2020 StreetScene981 46 35 203,257 56,847 146,410 159,341 43,916 205 17
2020XD-Violance104,754 - - --- - - - -
2022 UBnormal11‡*********** 236,902 116,087 92,640 147,887 89,015 29 660
†Followingprevious works, weset theframerateto15fps.‡TheUBnormaldatasetis supervised andincludes a
validationset with64videos. Italicized ones indicateWAED datasets,and underlinedones indicatemultimodaldataset.
Research on fully-unsupervised methods is still in its infancy, and they exploit the imbalance of
samplesand thesignificantdifferenceof anomalies in theGVAED task.
2.3 Benchmark Datasets
In Table 2, we show and compare the statistical results and properties of the frequently used
GVAED datasets. Several datasets [ 1,92,158,186] have been proposed with different annotated
signals to match new research requirements after 2018, which reflects the trend of GVAED from
unsupervisedtoweakly-unsupervised[ 158],fromunimodaltomultimodal[ 186]andfromsimple
to complexreal-world scenarios[ 92]at thedatalevel.
2.3.1 SubwayEntrance&Exit. Asanearlierdataset,Subway[ 2]includestwoindependentsub-
datasets, Entrance and Exit, which record the subway entrance and exit scenes, respectively. The
anomalous events include people who skip the subway entrance to evade tickets, cleaners who
behavedifferentlyfromregularentryandexit,andpeoplewhotravelinthewrongdirection.Due
to the cursory nature of the labeling work and the lack of clarity in the definition of anomalous
events,mostexistingworkshaverefrainedfromusingthisdatasetformodelevaluation.Therefore,
we do not provide quantitative performance comparison results on this dataset but only briefly
describeitscharacteristicstoreflectthelineageof GVAED datasetsdevelopment.
2.3.2 UMN. TheUMN[ 28]isalsoanearlyGVAEDdatasetcontaining11shortvideoscaptured
from three different scenes: grassland, indoor hall, and park. The scenes are set by the researcher
ratherthannaturallyfilmedtodetectabnormalcrowdbehaviorinindoorandoutdoorscenes,i.e.,
the crowd suddenly shifts from normal interaction to evacuation and flees abruptly to simulate
2https://vision.eecs.yorku.ca/research/anomalous-behaviour-data/sets/
3http://mha.cs.umn.edu/proj_events.shtml#crowd
4http://www.svcl.ucsd.edu/projects/anomaly/dataset.htm
5http://www.cse.cuhk.edu.hk/leojia/projects/detectabnormal/dataset.html
6https://svip-lab.github.io/dataset/campus_dataset.html
7https://webpages.charlotte.edu/cchen62/dataset.html
8https://github.com/jx-zhong-for-academic-purpose/GCN-Anomaly-Detection/
9https://www.merl.com/demos/video-anomaly-detection
10https://roc-ng.github.io/XD-Violence/
11https://github.com/lilygeorgescu/UBnormal
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:10 Y .L iue ta l.
fear. The anomalies are artificially conceived and played out, ignoring the diversity and rarity of
anomaliesinthereal-world.SimilartotheSubway[ 2]dataset,UMNhasbeenabandonedbyrecent
researchersdue tothelackofspatialannotation.
2.3.3 UCSD Pedestrian. UCSD Ped1 & Ped2 [ 84] are the most widely used UVAD datasets col-
lected from university campuses with simple but realistic scenarios. They reflect the value of
GVAEDinpublicsecurityapplications.Specifically,thePed1datasetiscapturedbyacamerawith
aviewpointperpendiculartotheroad,sothemovingobject’ssizechangeswithitsspatialposition.
Incontrast,thePed2datasetusedacamerawhoseviewpointisparalleltothedirectionoftheroad,
whichissimplerthanPed1.Pedestrianwalkingisdefinedasnormal,whilebehaviorsandobjects
different from it are considered abnormal, such as biking, skateboarding, and driving. Since the
sceneisclassicalandanomalouseventsareeasytounderstand,UCSDPedestrianiswidelyusedby
existingworks,andtheframe-level AreaUnderthereceiveroperatingcharacteristicsCurve
(AUC)has been as high as 99%, reflecting the saturation of model performance. Therefore, the
dataset in simple scenes has become a constraint for GVAED development. The large-scale and
cross-scenedatasetshavebecomean inevitabletrend.
2.3.4 CUHK Avenue. Similar to UCSD Pedestrian, the CUHK Avenue [ 107] dataset is also col-
lected from the university campus, and both focus on anomalous events that occur on the road
outside of expectations. The difference is that most of the 47 anomalous events in CUHK Av-
enue are simulated by the data collector, including appearance anomalies (e.g., bags placed on
the grass) and motion anomalies, such as throwing and wrong direction. CUHK Avenue provides
both frame-level and pixel-level spatial annotations. In addition, its large data scale makes it one
ofthemainstreamUVADdatasets.
2.3.5 ShanghaiTech. TheUCSDPedestrian[ 84]andCUHKAvenue[ 107]datasetsonlyconsider
anomalous events in a single scene, while the real world usually faces the challenge of spatial-
temporal pattern shifts across scenes. For this reason, the team from ShanghaiTech University
proposedtheShanghaiTech[ 92]datasetcontaining13scenes,providingthelargestUVADbench-
mark.Abnormalbehaviorsaredefinedasallcollectedbehaviorsthatdistinguishthemfromnormal
walking, such as riding a bicycle, crossing a road, and jumping forward. Unfortunately, although
the collectors pointed out the shortcomings of the existing dataset with a single scenario, their
proposed FFP [ 92] was not explicitly designed to address the cross-scene challenges but rather to
treat it as a whole without differentiating between scenarios. For the WAED setting, researchers
[220]proposedtomovesomeanomalousvideosfromthetestsettothetrainingsetandprovided
video-levellabelsforeachtrainingvideo,introducingtheShanghaiTechWeaklydataset,whichhas
become the mainstream WAED benchmark. A compelling phenomenon is that the performance
ofWAEDmethodsonShanghaiTechWeakly(frame-levelAUCistypically >85%andhasreached
upto95%)isgenerallyhigherthanthatofUVADmethodsontheShanghaiTech(frame-levelAUC
is typically between 70 ∼80%), providing evidence for the applicability of WAED in complex sce-
nariosoverUVAD.
2.3.6 UCF-Crime. UCF-Crime[ 158]isthefirstWAEDdataset,presentedtogetherwiththeorig-
inalMILrankingframework.UCF-Crimeconsistsof1,900uneditedreal-worldsurveillancevideos
collected from the Internet. The abnormal events contain 850 anomalies of human concern in 13
categories: Abuse,Arrest,Arson,Assault,Burglary,Explosion ,Fighting,Road Accidents ,Robbery,
Shooting,Shoplifting ,Stealing,andVandalism .UnliketheUVADdatasetabove,itstrainingsetcon-
tains anomalous videos and provides a video-level label for each video, where 0 indicates normal,
and 1 indicates anomalous. The anomalous events in the WAED dataset are predefined and are
usually associated with specific scenarios, such as car accidents in urban traffic, shoplifting, and
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:11
Fig. 6. Examples of XD-Violence dataset [ 186]. XD-Violence is a multimodal dataset for violence detection,
includingvideoandaudio.Weshowvideoframeshere.Theanomalouseventsarenotallfromthereal-world
but alsoinclude movie andgame footage, etc.
shootingsinneighborhoods.Therefore,WAEDcanprovidemorecredibleresultsforrealscenarios
withbetterapplicationpotential.
2.3.7 XD-Violence. Asthefirstaudio-videodataset,XD-Violence[ 186]expandsanomalyevent
detectionfromsingle-modalvideounderstandingtomultimodalsignalprocessing,facilitatingthe
coexistence of GVAED and multimedia communities. XD-Violence focuses on violent behaviors,
such as abuse, explosion, car accident, struggle, shootings, and riots, as shown in Figure 6.D u e
to the rarity of violent behaviorsand the high difficulty of capturingviolence, the original videos
include some movie clips in addition to real-world surveillance videos. XD-Violence provides a
new way to think about the GVAED by extending the data modality from single videos to sound,
text, andothers.
2.3.8 UBnormal. Inspired by the computer vision community benefiting from synthetic data,
Acsintoae et al.[1] propose the first GVAED benchmark with virtual scenes, named UBnormal.
Notably, utilizing a data engine to synthesize data under predetermined instructions rather than
collecting real-world data makes pixel-level labeling possible. Therefore, UBnormal is supervised.
UBnormal is built to address the problem that WAED ignores the open-set nature of anomalies
that prevents the model from correctly corresponding to new anomalies. The test set contains
anomalous events not present in the training set. Moreover, it provides a validation set for model
tuningfor thefirsttime.
2.4 Performance Evaluation
Existing GVAED methods evaluate model performance in terms of detection accuracy and oper-
ational cost. The former concerns the ability to discriminate anomalous events while the latter
aims to measure the deployment potential on resource-limited devices. According to the scale
of detected anomalies, the detection accuracy criteria are divided into three levels: Temporal-
Detection-Oriented (TDO) ,Object-Detection-Oriented (ODO) ,a n dSpatial-Localization-
Oriented (SLO) . Specifically, TDO criteria require the model to determine anomalous events’
startingandendingtemporalpositionwithoutspatiallocalizationofabnormalpixels.Incontrast,
ODO criteria include object-level, region-level, and track-level, focusing on specific anomaly ob-
jectsortrajectories.SLOcriteriaencouragepixel-levellocalizationofanomalousevents.Asforop-
erationalcostcriteria,thecommonlyusedmetricincludeparametersize,inferencespeed,andthe
number of FLOating Point operations (FLOPs) on thesame platform,asshownin Figure 7(a).
Wecanevaluatethemodelperformancequantitativelybycomparingthepredictedresultswith
the ground truth labels. It is worth noting that the predicted labels of some GVAED models (e.g.,
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:12 Y .L iue ta l.
Fig. 7. Illustration of GVAED performance evaluation system. We show the (a) metrics system and (b) con-
fusionmatrix.
prediction-based UVAD and WAED) are continuous values in the range of [0,1]. In contrast, the
truelabelsarediscrete0or1,soathresholdvaluemustfirstbeselectedwhencalculatingtheper-
formancemetrics.Sampleswithabnormalscoresbelowthethresholdareconsiderednormal,and
vice versa. In this way, we obtain the confusion matrix shown in Figure 7(b), where TP, FN, FP,
andTNdenotethenumberofabnormalsamplescorrectlydetected,abnormalsamplesmistakenly
detected as normal, normal samples mistakenly detected as abnormal, and normal samples cor-
rectlydetected,respectively.The True-Positive-Rate(TPR) ,False-Positive-Rate(FPR) ,True-
Negative-Rate (TNR) ,andFalse-Negative-Rate (FNR) aredefinedas follows:
TPR=TP
TP+FN;FPR=FP
FP+TN;TNR=TN
FP+TN;FNR=FN
TP+FN(4)
whichareusedtocalculatethe AreaUndertheReceiverOperatingCharacteristic(AUROC)
andAveragePrecision (AP) .
AUROC: The horizontal and vertical coordinates of the Receiver Operating Characteristic
(ROC)curvearetheFPRandTPR,andthecurveisobtainedbycalculatingtheFPRandTPRunder
multiple sets of thresholds. The area of the region enclosed by the ROC curve and the horizontal
axisisoftenusedtoevaluatebinaryclassificationtasks,denotedasAUROC.ThevalueofAUROC
iswithintherangeof[0,1],andhighervaluesindicatebetterperformance.AUROCcanvisualize
the generalization performance of the GVAED model and help to select the best alarm threshold.
Inaddition,the EqualErrorRate(EER) ,i.e.,theproportionofincorrectlyclassifiedframeswhen
TPRandFNR areequal,is alsousedto measuretheperformanceof anomaly detectionmodels.
AP:DuetothehighlyunbalancednatureofpositiveandnegativesamplesinGVAEDtasks,i.e.,
the TN is usually larger than the TP, researchers think that the area under the Precision-Recall
(PR)curve is more suitable for evaluating GVAED models, denoted as AP. The horizontal coordi-
nates of the PR curve are the Recall (i.e., the TPR in Equation ( 4)), while the vertical coordinate
representsthePrecision,definedasPrecision =TP
TP+FP.ApointonthePRcurvecorrespondstothe
Precision and Recall values at a certain threshold. Currently, AP has become the main metric for
multimodalGVAEDmodels[ 181,186,187]andiswidelyusedtoevaluatetheperformanceonthe
XD-Violencedataset[ 186].
3 UNSUPERVISED VIDEO ANOMALY DETECTION
Existing reviews [ 125,139] usually classify UVAD methods into distance-based [ 29,30,148],
probability-based [ 6,23,146,149], and reconstruction-based [ 46,50,101] according to the devi-
ationscalculationmeans.Earlytraditionalmethodsreliedonmanualfeaturessuchasforeground
masks [148], histogram of flow [ 30], motion magnitude [ 148], HOG [29], dense trajectories [ 173],
and STIP [ 34], which relied on human ap r i o r iknowledge and had poor representational power.
Withtheriseofdeeplearningincomputervisiontasks[ 151,209,217],recentapproachespreferred
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:13
toextractingfeaturesrepresentationsinanend-to-endframeworkwithdeep Auto-Encoder(AE)
[24,50,97,101,131],Generative Adversarial Network (GAN) [9,17,59,60,75,126,214], and
Vision Transformer(ViT) [40,70,204].
This sectionis dedicatedto providinga systematicoverview of UVAD methods driven by deep
learningtechniques[ 21,22,32,95,103].It’sworthnotingthatthetraditionaltaxonomy,asoutlined
inpreviousstudies[ 125,139],predominantlyfocusesonmanualfeature-basedmethods,whichare
limitedinelucidatingtheevolvinglandscapeofdeepUVADmodels.DeepCNNsexhibitaremark-
able capacity for modeling the spatiotemporal intricacies within video sequences and generating
profoundrepresentationsofvarioussensorydomains,contingentuponthenatureoftheinputdata.
Consequently,wecategorizetheworkfoundinexistingliteratureintothreeprincipalgroups,con-
tingent upon the nature of the data employed: (1) Frame-level methods always utilize entire
RGB and optical flow frames as input and endeavor to capture a holistic understanding of nor-
mality within the video data. Such approaches consider the entirety of the frame, aiming to com-
prehend the global context. (2) Patch-level methods recognize the repetitive spatial-temporal
information present in video sequences, so they extract features solely from designated regions
of interest. They intentionally disregard redundant data from repetitive regions and interactions
amongregionalinformationthatdonotwarrantparticularattention.Thisstrategyoffersdistinct
advantages in terms of computational efficiency and inference speed. (3) Object-level methods ,
emerginginrecentyearswiththedevelopmentoftargetdetectionmodels,shiftthefocustowards
detecting foreground objects and scrutinizing the behavior of specific objects within the video
context. Object-level methods consider the relationship between objects and their backgrounds,
showcasing impressive performance in the task of identifying anomalous events within complex
scenes. Based on the aforementioned analysis, this section classifies UVAD methods into three
distinct categories: frame-level, patch-level, and object-level. This categorization is aligned with
a hierarchical “input-structure” taxonomy shown in Figure 1. This taxonomy serves as a guiding
framework for organizingand understandingthelandscapeof UVAD.
3.1 Frame-Level Methods
Deep CNNs can directly extract abstract features from videos and learn task-specific deep repre-
sentations. Frame-level methods use complete RGB frames, sequences, or optical flows as input
to model the normality of normal events in a self-supervised learning manner. Existing methods
canbeclassifiedintotwocategoriesaccordingtomodelstructure:single-streamandmulti-stream.
The former does not distinguish spatial and temporal information. They usually take the original
RGBvideosasinputandlearnthespatial-temporalpatternsbyreconstructingtheinputsequence
orpredictingthenextframe.Existingsingle-streamworkfocusesondesigningmoreefficientnet-
workstructures.Theyintroducemorepowerfulrepresentationallearnerssuchas3Dconvolution
[219] and U-net [ 92]. In contrast, multi-stream networks typically treat appearance and motion
asdifferentdimensionsofinformationand attempttolearnspatialandtemporalnormalityusing
different agent tasks or network architectures. In addition to spatial-temporal separation model-
ing, existing dual-stream works explored spatial-temporal coherence [ 97] and consistency [ 8]t o
performanomaly detection.
3.1.1 Single-Stream Models. Single-stream models typically use a single generative model
to describe the spatial-temporal patterns of normal events by performing a proxy task and
preserving the normality in learnable parameters. For example, the Predictive Convolutional
Long Short-Term Memory(PC-LSTM) [121] used a conformingConvLSTM networkto model
the evolution of video sequences. Hasan et al. [ 50] constructed a fully convolutional Feed-
Forward Auto-Encoder (FF-AE) with manual features as input, which can learn task-specific
representationsinan end-to-endmanner.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:14 Y .L iue ta l.
Liuetal.[ 92]proposeda FutureFramePrediction(FFP) methodthatusedaGAN-basedvideo
predictionframeworktolearnthenormality.Itsextension,FFPN[ 112],furtherspecifiedthedesign
principlesofpredictiveUVADnetworks.SinghandPankajakshan[ 155]alsousedapredictivetask
todetectanomalies,proposingconformalstructuresbasedon2D&3DconvolutionandconvLSTM
tocharacterizespatial-temporalpatternsmore efficiently.
Toaddressthedetaillossinframegeneration,Lietal.[ 85]proposeda Spatial-TemporalU-net
network (STU-net) that combined the advantages of U-net in representing spatial information
with the ability of convLSTM to model temporal variations for moving objects. [ 221]p r o p o s e d
a sparse coding-based neural network called AnomalyNet, which used three neural networks to
integrate the advantages of feature learning, sparse representation, and dictionary learning. In
[124], the authors proposed an Incremental Spatial-Temporal Learner (ISTL) to explore the
nature of anomalous behavior over time. ISTL used active learning with fuzzy aggregation to
continuously update and distinguish between new anomalous and normal events evolving. The
anoPCNin[ 198]unifiedthereconstructionandpredictionmethodsintoadeeppredictivecoding
network by introducing an error refinement module to reconstruct the prediction errors and
refiningthecoarsepredictionsgeneratedbythepredictivecodingmodule.
To lessen the deep model’s ability to generalize anomalous samples, memory-augmented
Auto-Encoder(memAE) [46]embeddedanexternalmemorynetworkbetweentheencoderand
decodertorecordtheprototypicalpatternsofnormalevents.Further,Parketal.[ 131]introduced
anattention-basedmemoryaddressingmechanismandproposedtoupdatethememorypooldur-
ingthetestingphaseto ensurethatthenetworkcanbetterrepresentnormalevents.
Luoetal.[ 113]proposedasparsecoding-inspiredneuralnetworkmodel,namely Temporally-
coherent Sparse Coding (TSC) . It used a sequential iterative soft thresholding algorithm to
optimize the sparse coefficients. [ 31] introduces residual connection [ 53] into the auto-encoder
to eliminate the gradient disappearance problem during normality learning. Experiments have
shown that ResNetr brought 3%, 2% and 5% frame-level AUC gains for the proposed Residual
Spatial-Temporal Auto-Encoder (R-STAE) on CUHK Avenue [ 107], LV [72] and UCSD Ped2
[84] datasets,respectively.
The DD-GAN in [ 35] introduced an additional motion discriminator to GAN. The dual
discriminators structure encouraged the generator to generate more realistic frames with motion
continuity. Yu et al. [ 202] also used GAN to model normality. The proposed Adversarial Event
Prediction (AEP) network performed adversarial learning on past and future events to explore
the correlation. Similarly, Zhao et al. [ 218] explored spatial-temporal correlations by GAN and
usedaspatial-temporalLSTMtoextractappearanceandmotioninformationwithinaunifiedunit.
[16]proposeda BidirectionalPrediction(Bi-Pre) frameworkthatusedforwardandbackward
prediction sub-networks to reason about normal frames. In the test phase, only part significant
regions are used to calculate the anomaly score, allowing the model to focus on the foreground.
Wangetal.[ 178]usedmulti-pathconvGRUtoperformframeprediction.TheproposedROADMAP
model includedthreenon-local modulesto handledifferent scalesofobjects.
3.1.2 Multi-StreamModels. Themultiplicity ofmulti-streammodelsisreflectedinthemultiple
sources of the input data and the multiple tasks corresponding to multiple outputs. Considering
that video anomaly may manifest as outliers in appearance or motion, an intuitive idea is to use
multi-stream networks to model spatial and temporal normality separately [ 13,14,82,175,188].
Inaddition,learningassociationsbetweenappearanceandmotion,suchasconsistency[ 8],coher-
ence [97,101], and correspondence [ 127], is another effective GVAED solution. Events without
such associations are discriminated against as anomalies. The multi-stream model has achieved
significant success in recent years due to the high matching of its design motivation with the
GVAEDtask.Themulti-streammethodsare summarized inTable 3.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:15
Table 3. Frame-Level Multi-StreamUVAD Methods
Year Method Backbone Analysis
2017 AMDN[ 191]A EPros:Learningappearance andmotion patternsseparately.
Cons:Determiningboundariesby OC-SVMwithlimitedcapability.
2017 STAE[ 219]A EPros:Using 3DCNN to learn thespatial-temporalpatterns.
Cons:Dualdecoderscausing hugecomputationalcosts.
2019 AMC[ 127]G A NPros:Learningthe correspondencebetweenappearance and motion.
Cons:Limitedperformance on thecomplex datasets.
2019 GANs[ 142]G A NPros:Training two GANs tolearn temporaland spatialdistribution.
Cons:Unstable training process and hightraining cost.
2020 CDD-AE[ 14]A EPros:Using two auto-encodersto learn spatial andtemporalpatterns.
Cons:No specialconsideration in thedesign oftheencoderstructure.
2020 OGNet [ 205]G A NPros:Using generatorsand discriminators tolearn normality.
Cons:Adversariallearning making the training processunstable.
2021 AMMC-net[ 8]A EPros:Exploring theconsistency ofappearance andmotion.
Cons:Lackof analysis tothe flow-frame generationtask.
2021 DSTAE [ 82] AE,ConvLSTMPros:Using two auto-encodersto performdifferenttasks.
Cons:Highcomputationalcost and relying on optical flownetwork.
2022 AMAE[ 97]A EPros:Usingtwo encodersand threedecoderstolearn features.
Cons:Hightraining cost and relying onoptical flownetwork.
2022 STM-AE[ 101]A E , G A NPros:Using two memory-enhancedauto-encodersto learn normality.
Cons:Unstable training process and highcomputationalcosts.
Motivated by the remarkable success of 3D CNN in video understanding tasks, Zhao et al.
[219] proposed a 3D convolutional-based Spatial-Temporal Auto-Encoder (STAE) to model
normality by simultaneously performing reconstruction and prediction tasks. STAE included
two decoders, which outputted reconstructed and predicted frames, respectively. In contrast,
Appearance and Motion DeepNet (AMDN) [191] used two stacked denoising auto-encoders
to encode RGB frames and optical flow separately. Similarly, Chang et al. [ 14]a l s ou s e dt w o
auto-encoders to capture spatial and temporal information, respectively. One learned the appear-
ance by reconstructing the last frame, while the other outputted RGB differences to simulate
the generation of optical flow. Deep K-means clustering was used to force the extracted feature
compact and detect anomalies. DSTAE [ 82] introduced convLSTM to a two-stream auto-encoder
to better model the temporal variations. The reconstruction errors of the two encoders are
weightedand usedto calculateanomaly scores.
In addition to spatial-temporal separation, Nguyen and Meunier [ 127] proposed to learn the
correspondence between appearance and motion. To this end, they proposed an AE with two
decoders, one for reconstructing input frames and the other for predicting optical flow. Cai et al.
[8]p r o p o s e da n Appearance-Motion Memory Consistency network (AMMC-net) ,w h i c h
aimed to capturethespatial-temporalconsistencyin high-levelfeaturespace.
Liu et al. [ 97]p r o p o s e da n Appearance-Motion united Auto-Encoder (AMAE) framework
usingtwoindependentauto-encoderstoperformdenoisingandopticalflowgenerationtaskssep-
arately.Moreover,theyutilizedanadditionaldecodertofusespatial-temporalfeaturesandpredict
future frames to model spatial-temporal normality. STM-AE [ 101] and AMP-Net [ 99] introduced
the memory into the dual-stream auto-encoder to record prototype appearance and motion pat-
terns. Adversarial learning was used to explore the connection between spatial and temporal in-
formation of regularevents.
Aside from the above anomaly detection means such as reconstruction error [ 82,97,101,219],
clustering [ 13,14] and one-class classification [ 191], researchers attempted to utilize the discrim-
inator of GAN to directly output results. For instance, Ravanbakhsh et al. [ 142]u s e dG A Nt o
learn the normal distribution and detect anomalies directly by discriminators. The authors use a
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:16 Y .L iue ta l.
cross-channel approach to prevent the discriminator from learning mundane constant functions.
OGNet[205]shiftedthediscriminatorfromdiscriminatingrealorgeneratedframestodistinguish-
inggoodorpoorreconstructions.Thewell-traineddiscriminatorcanfindsubtledistortionsinthe
reconstructionresultsand detectnon-obviousanomalies.
3.2 Patch-Level Methods
Thepatch-levelmethods[ 96,118,145,147]takesthevideopatch(spatial-temporalcube)asinput.
Comparedwithframe-levelmethodsthatconsideranomaliesroughly,i.e.,anomaliesarereflected
inspatialortemporaldimensionsbeyondexpectation,patch-levelmethodsconsiderfindinganom-
alies from specific spatial-temporal regions rather than analyzing the whole sequence. Patch for-
mation can be divided into three categories: scale equipartition [ 25,78,96,118,147,185,222],
informationequipartition[ 73],andforegroundobjectextraction[ 177].Specifically,scaleequipar-
titionisthesimplest.Thevideosequenceisequipartitionedintoseveralspatial-temporalcubesof
uniformsizealongthespatialandtemporaldimensions.Thesubsequentmodelingprocessissim-
ilar to frame-level methods. The information equipartition strategy considers that image blocks
of the same size do not contain the same information. Regions close to the camera contain less
informationperunitareathanthosefaraway.Beforerepresentation,allcubeswillbefirstresized
tothesamesize.Theforegroundobjectextractionfocusesonmodelingregionswithinformation
variation to avoid the learning cost and disruption of the background. After the sequences are
equatedintosame-scale cubes,thosecontainingonly backgroundwillbeeliminated.
RoshtkhariandLevine[ 145]denselysampledvideosequencesatdifferentspatialandtemporal
scales and used a probabilistic framework to model the spatial-temporal composition of the
video volumes. The STCNN [ 222] treated UVAD as a binary classification task. It first extracted
patches’ appearance and motion information and outputted the discriminative results with an
FCN. It first equated the video sequence into patches of 3 ×3×7 and retained only the part
of the region containing moving pixels to ensure the robustness of the model to local noise
and improve the detection accuracy. Deep-Cascade [ 147] employed a cascaded autoencoder to
representvideopatches.Itusedalightweightnetworktoselectlocalpatchesofinterestandthen
applied a complex 3D convolutional network to detect anomalies. The lightweight network can
filter simple normal patches to reduce computational costs and save processing time. S2-VAE
[177] first detected the foreground and retained only the cell containing the object as input.
And then, a shadow generative network was used to fit the data distribution. The output was
fed to another deep generative network to model normality. Wu et al. [ 185]p r o p o s e da deep
one-class neural network (DeepOC) . Specifically, DeepOC used stacked auto-encoders to
generatelow-dimensionalfeaturesforframeandopticalflowpatchesandsimultaneouslytrained
theOCclassifiertomake theserepresentationsmore compact.
Spatial-Temporal Cascade Auto-Encoder (ST-CaAE) [78] first used an adversarial autoen-
codertoidentifyanomalousvideosandexcludedsimpleregularpatches.Theretainedpatcheswere
fedtoaconvolutionalautoencoder,whichdiscriminatedanomaliesbasedonreconstructionerrors.
L i ue ta l .[ 96]p r o po s eda n Attention augmented Spatial-Temporal Auto-Encoder (AST-AE)
that equated frames in spatial dimensions into 8 ×8 parts and models spatial and temporal infor-
mation using CNN and LSTM, respectively. In the downstream anomaly detection stage, AST-AE
only retainedsignificant regionswithlarge predictionerrorstocalculatetheanomaly score.
3.3 Object-Level Methods
The emergence of high-performance object detection models [ 45,143,192] provides a new idea
forGVAED,i.e.,usingapre-trainedobjectdetectortoextracttheobjectsofinterestfromthevideo
sequencebeforenormalitylearning.Comparedwiththeframe-levelandpatch-levelmethods,the
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:17
Table 4. Object-Level UVADMethods
Year Method Detector Decision Logic Contributions
2017 LDGK[ 54]F a s t
R-CNNAnomalyscore of
thedetectedobject
proposalIntegratinga genericCNNand environment-relatedanomaly
detectortodetectvideoanomaliesandrecordthecauseofthe
anomalies.
2018 DCF[ 68] YOLO Classification Extractingforeground objects byobjectdetectionmodels and
Kalmanfilteringanddiscriminatinganomaliesbypose and
motionclassification.
2019 OC-AE [ 62] SSD One-versus-rest
binaryclassificationProposing anobject-centric convolutional autoencoder to
encode motionandappearanceanddiscriminating anomalies
using a one-versus-rest classifier.
2021 Background-
Agnostic
[44]SSD-FPN,
YOLOv3Binaryclassification Using asetof autoencoders to extracttheappearanceand
motionfeaturesof foreground objectsand thenusing asetof
binaryclassifiers todetectanomalies.
2021 Multi-task
[43]YOLOv3 Binaryclassification Traininga3Dconvolutional neuralnetwork togenerate
discriminativerepresentationbyperforming multiple
self-supervised learningtasks.
2021 OAD [ 37] YOLOv3 Clustering An onlineVAD method withasymptoticboundson the false
alarmrate, providingaprocedure forselectinga proper
decision threshold.
2021 HF2-VAD
[105]Cascade
R-CNNPrediction error A hybrid frameworkthatseamlesslyintegratessequence
reconstruction andframepredictionto handlevideo anomaly
detection.
2020 VEC [ 201]C a s c a d e
R-CNNCubeconstruction
errorProposing avideo eventcompletionframeworktoexploit
advancedsemanticandtemporalcontextualinformationfor
video anomalydetection.
2022 BiP [ 15]C a s c a d e
R-CNNAppearanceand
motionerrorProposing a bi-directional architecture withthree consistency
constraints to regularize theprediction taskfrom thepixel,
cross pattern,andtemporallevels.
2022 HSNBM [ 5]C a s c a d e
R-CNNFrameandobject
predictionerrorDesigningahierarchical scenenormative bindingmodeling
frameworktodetectglobalandlocalanomalies.
object-level methods [ 44,93,160,200] enable the model to ignore redundant background infor-
mation and focus on modeling the behavioral interactions of foreground objects. In addition to
outperforming object-free methods in terms of performance, object-level methods are also con-
sidered feasible to investigate scene-adaptive GVAED models. Existing studies [ 43,44] show that
object-levelmethodsperformsignificantlybetterthanothermethodsonmulti-scenedatasetssuch
asShanghaiTech[ 92].Table4comparestheobjectdetectors,decisionlogic,andmaincontributions
of existingobject-levelmethods.
Hinami et al. [ 54] attempted to describe anomalous events in a human-understandable form
bydetectingandanalyzingtheclasses,behaviors,andattributesofspecificobjects.Theproposed
LDGK model first used multi-task learning to obtain anomaly-related semantic information and
then inserted an anomaly detector to analyze scene-independent features to detect anomalies.
The DCF [ 68] used a pose classifier and an LSTM network to model the spatial and motion in-
formation of the detected objects, respectively. [ 62] formalizes UVAD as a one-versus-rest binary
classification task. The proposed OC-AE first encoded the motion and appearance of selected
objects and then clustered the training samples into normal clusters. An object is considered
anomalous in the inference stage if the one-versus-rest classifier’s highest classification score is
negative. Its extension, the Background-Agnostic framework [ 44], introduced instance segmen-
tation, allowing the model to focus only on the primary object. In addition, the authors used
pseudo-anomalyexamplestoperformadversariallearningtoimprovetheappearanceandmotion
auto-encoders.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:18 Y .L iue ta l.
To make full use of the contextual information, Yu et al. [ 201]p r o p o s e da Video Event
Completion(VEC) methodthatusedappearanceandmotionascuestolocateregionsofinterest.
VEC recovered the original video events by solving visual completion tests to capture high-level
semantics and inferring deleted patches. Georgescu et al. [ 43] designed several self-supervised
learning tasks, including discrimination of forward/backward moving objects, discrimination of
objects in continuous/intermittent frames, and reconstruction of object-specific appearance. In
thetestingphase,anomalous objectswould lead tolarge predictiondiscrepancies.
Doshi and Yilmaz [ 37]p r o p o s e da n Online Anomaly Detection (OAD) scheme that used
detected object information such as location, category, and size as input to a clustering model to
detectanomalousevents.HF2-VAD [105]seamlesslyintegratedframesreconstructionandpredic-
tion.Itusedmemorytorecordthenormalpatternofopticalflowreconstructionandcapturedthe
correlationbetweenRGB framesand opticalflowusinga conditionalvariationauto-encoder.
Chenetal.[ 15]proposeda BidirectionalPrediction(BiP) architecturewiththreeconsistency
constraints. Specifically, prediction consistency considered the symmetry of motion and appear-
ance in forward and backward prediction. Association consistency considered the correlation be-
tweenframesandopticalflow,andtemporalconsistencywasusedtoensurethatBiPcangenerate
temporallyconsistentframes.
Insummary,object-levelmethods,employingwell-trainedobjectdetection/segmentationmod-
elstoisolatesignificantforeground targetsfromvideoframesand developingscene-independent
anomaly detection models through analysis of target-specific attributes, offer notable advantages
over frame-level and patch-level approaches in real-world cross-scene datasets. However, these
methodsfacechallengesincapturingtheinteractionbetweenscenesandbackgrounds,leadingto
performance degradation in handling scene-specific anomalous events, such as a person walking
on a motorway. Addressing this limitation, Liu et al. [ 93] explored the semantic interaction
between prototypical features of foreground targets and the background scene using memory
networks. Alternatively, instance segmentation proves more effective in modeling target-scene
interactions. For instance, the Hierarchical Scene Normality-Binding Modeling (HSNBM)
framework [ 5] attempted to dissect global and local scenes, which introduced a scene object-
bindingframepredictionmoduletocapturetherelationshipbetweenforegroundandbackground
through scene segmentation. Looking ahead, object-level methods with object detection or
instance segmentation will play a crucial role in discovering anomalous events in real-world
highlydynamic environments,suchasautonomousdrivingand intelligent industries.
4 WEAKLY-SUPERVISED ABNORMAL EVENT DETECTION
Usingweaklysemanticvideo-levellabelstosupervisethemodelwasfirstproposedbySultanietal.
[158]in2018,layingthefoundationforWAEDbasedonmultipleinstancelearning[ 114,132,211].
The synchronously released UCF-crime dataset collected 13 classes of real-world criminal behav-
iorsandprovidedvideo-levellabelsfortrainingsets.Followingresearchers[ 164,220]madeUV AD
datasets meet WAED requirements by moving some anomalous test videos to the training set, in-
troducingvariousWAEDbenchmarkssuchasthereorganizedUCSDPed2[ 98]andShanghaiTech
Weakly [79,220]. In 2020, the XD-Violence [ 186] dataset extended GVAED to multimodal signal
processing.
This section is dedicated to providing an in-depth exploration of existing WAED models, with
afocusontheircategorizationintounimodalandmultimodalapproachesbasedontheinputdata
modalities. This taxonomy is instrumental in guiding the development of GVAED methods, fos-
tering the transition from video processing to a broader multimodal understanding communities.
Unimodal models [39,79,102,158,164,208,220], similar to UVAD techniques, primarily rely
on RGB frames as input data. However, they distinguish themselves by directly computing the
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:19
anomaly score. These models center their efforts on analyzing successive RGB frames to detect
anomalies within the videos. In contrast, multimodal models [19,181,186,187,203]a i mt o
leverage diverse data sources, including video, audio, text, and optical flow, to extract effective
anomaly-related clues. These methods harness the power of multiple modalities to enhance the
overallunderstandingofanomalies,makingthemmorerobustandversatileincapturingcomplex
abnormalevents.Thiscategorizationschemenotonlyclarifiesthedistinctionsbetweenunimodal
andmultimodalWAEDmodelsbutalsosetsthestagefortheevolutionofGVAEDtechniquesthat
integratevariousdatamodalities,pavingthewayforamorecomprehensiveapproachtoanomaly
detectionandevent understanding.
4.1 Unimodal Models
The unimodal WAED model typically slices the unedited video into several fixed-size clips. They
consider each clip as an instance, and all clips from a video form a bag with the same video-level
label.Andthen,pre-trainedfeatureextractors,suchas Convolutional3D(C3D) [165],Temporal
Segment Networks (TSN) [176], andInflated 3D (I3D) [11], are used to extract the spatial-
temporal features of the examples. Generally, the scoring module takes deep representations as
inputandcalculatestheanomalyscoreforeachinstancewiththesupervisionofvideo-levellabels.
The MIL ranking framework [ 158] introduced multiple instance learning to GVAED for the
first time, using a 3-layer Fully Connected Network (FCN) to predict high anomaly scores for
anomalous clips and introducing sparsity and smooth constraints to avoid drastic fluctuations in
the score curve. Zhu and Newsam [ 223] considered motion as the key to WAED performance.
To this end, they proposed a temporal augmented network to learn motion-aware features and
used attention blocks [ 89] to incorporate temporal context into a MIL ranking model. Majhi et al.
[119] used a dual-stream CNN to extract spatial and temporal features separately and fed the
fused features as spatial-temporal representations into an FCN to perform anomaly classification.
TheauthorscomparedtheperformanceofdifferentdeepCNNarchitectures(e.g.,ResNet-50[ 53],
InceptionV3[ 161],and VGG-16 [ 157]) for featureextraction.
Zhongetal.[ 220]treatedWAEDasasupervisedlearningtaskundernoisylabels,arguingthat
the supervised action recognition models can perform anomaly detection after the label noise is
removed.Inresponse,theydesignedagraphconvolutionalnetworktocorrectthelabels.[ 172]pro-
posedAnomaly Regression Network (ARNet) to learn discriminative features WAED. Specifi-
cally,ARNetuseddynamicmultiple-instancelearninglossandcenterlosstoenlargetheinter-class
distanceinstancesand reducetheintra-classdistanceof regular instances,respectively.
Waseemetal.[ 80]proposedatwo-stageWAEDframeworkthatfirstusedanechostatenetwork
toobtainspatiallyandtemporallyawarefeatures.Andthen,theyuseda3Dconvolutionalnetwork
toextractspatial-temporalfeaturesandfusethemwiththefeaturesfromthefirststageastheinput
toa binaryclassifier.Tianetal.[ 164]pr o po sed RobustTemporalFeature Magnitude (RTFM)
learning by training a feature volume learning function to identify positive examples efficiently.
Inaddition,RTFMutilizedself-attentiontocapturebothlongandshort-timecorrelations.Zaheer
et al. [208] proposed a self-reasoning framework that uses binary clustering to generate pseudo-
labels tosupervisetheMILregressionmodels.
TheCLustering Assisted Weakly Supervised (CLAWS) learning with normalcy suppres-
sion in [206] proposed a random batch-based training strategy to reduce the correlation between
batches. In addition, the authors introduced a loss based on clustering distance to optimize the
network to weaken the effect of label noise. Kamoona et al. [ 66]p r o p o s e da Deep Temporal
Encoding-Decoding(DTED) tocapturethetemporalevolutionofvideosovertime.Theytreated
instances of the same bag as sequential visual data rather than as independent individuals. In
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:20 Y .L iue ta l.
addition,DTEDusesjointlosstooptimizetomaximizetheaveragedistancebetweennormaland
abnormalvideos.
TheWeakly-supervised Temporal Relationship (WSTR) learning framework [ 212]e n -
hanced the model’s discriminative power by exploring the temporal relationships between clips.
Theproposedtransformer-enabledencoderconvertsthetask-irrelevantrepresentationsintotask-
specific features by mining the semantic correlations and positional relationships between video
clips.WeaklySupervisedAnomalyLocalization(WSAL) [115]performedanomalydetection
by fusing temporal and spatial contexts and proposed a higher-order context encoding model to
measure temporal dynamic changes. In addition, the authors collected a dataset called TAD for
trafficanomaly detection.
Feng et al. [ 39]p r o p o s e da Multi-Instance Self-Training (MIST) framework consisting of a
multi-instance pseudo label generator and a self-guided attention-enhancing feature encoder for
generatingmorereliablefragment-levelpseudolabelsandextractingtask-specificrepresentations,
respectively.Liuetal.[ 98]proposeda Self-guidingMulti-instanceRanking(SMR) framework
that used a clustering module to generate pseudo labels to aid the training of supervised multi-
instanceregressionmodelstoexploretask-relevantfeaturerepresentations.Theauthorscompared
theperformanceofdifferentrecurrentneuralnetworksinexploringtemporalcorrelation. Spatial-
TemporalAttention(STA) [102]exploredtheconnectionbetweenexamplelocalrepresentations
and global spatial-temporal features through a recurrent cross-attention operation and used mu-
tualcosinelossto encouragetheenhancedfeaturestobetaskspecific.
4.2 Multimodal Models
TheemergenceofTVshowsandstreamingmediahasbroadenedtheapplicationscopeofGVAED
technicals, transitioning them from traditional offline surveillance video analysis to online video
streamdetection.Unlikesurveillancevideos,whichtypicallyconsistofonlyRGBimages,moston-
linevideocontent,includingvlogs,livestreams,andtalkshows,incorporatesmultiplemodalities
such as language, speech, and subtitle text. Extracting anomaly-related cues from these diverse
datamodalitiesexceedsthecapabilitiesof currentunimodalmethods.
Real-worlddataareheterogeneous,andeffectivelyexploitingthecomplementarynatureofmul-
timodal data is the key to developing robust and efficient GVAED models. Due to the limitation
of datasets, most existing works [ 186,187,203] focused on video and audio information fusion
to detect violent behaviors from surveillance videos. Moreover, inspired by the frame-level multi-
stream UVAD models [ 82,97] ,r e c e n tw o r k[ 181] considered RGB frames and optical flow as dif-
ferentmodalities.WedisplaythemodalitiesandprinciplesofexistingmultimodalGVAEDmodels
[130,133,134,153,181,182,186,187,203]inT abl e 5.
Wu et al. [ 186] released the first multimodal GVAED dataset and proposed a three-branch net-
work called HL-Net for multimodal violence detection. Specifically, the similarity branch used a
similaritypriortocapturelong-rangecorrelations.Incontrast,theproximitybranchusedproxim-
ity prior to capture local location relationships, and the scoring branch dynamically captured the
proximity of predicted scores. Experimental results demonstrated the multimodal data’s positive
impact on GVAED. The following MACIL-SD in [ 203] utilized a lightweight dual-stream network
toovercometheheterogeneitychallenge.Itusedself-distillationtotransferunimodalvisualknowl-
edgeto audio-visualmodels tonarrow thesemanticgapbetweenmultimodal features.
Researchers [ 130,134] attempted to explore more effective feature extraction and multimodal
information fusion strategies. For example, Pang et al. [ 130] proposed to use a bilinear pooling
mechanismtofusevisualandaudioinformationandencouragethemodeltolearnfromeachother
toobtainamoreeffectiverepresentation. Audio-GuidedAttentionNetwork(AGAN) [134]first
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:21
Table 5. MultimodalWAEDModels
Year Method InputModality Contributions
2020 HL-Net [ 186] Video+ Audio Collectingthe XD-Violenceviolence detection datasetsand
proposingathree-branchneural networkmodel for
multimodal anomalydetection.
2021 FVAI [ 130] Video+ Audio Proposingapooling-based featurefusionstrategytofuse
video andaudio information toobtain morediscriminative
featurerepresentations.
2022 SC [ 133] Video+ Audio Proposinganaudio-visualscene classification dataset
containing fiveclasses of anomalous events anda deep
classification model.
2022 MACIL-SD[ 203] Video+ Audio Proposingamodality-aware contrastiveinstance learning
witha self-distillationstrategytoaddressthemodality
heterogeneity challenges.
2022 ACF [ 182] Video+ Audio Proposingatwo-stagemultimodalinformationfusionmethod
for violence detection that firstrefines video-level labels into
clip-level labels.
2022 MSAF [ 181] Video+ Audio,
Video+ Optical
flowProposingmultimodallabels refinement torefinevideo-level
groundtruthintopseudo-clip-level labels andimplicitly align
multimodal informationwithmultimodal supervise-attention
fusionnetwork.
2022 MD[ 153] Video+ Audio +
FlowUsingmutual distillationtotransferinformation and
proposingamultimodal fusionnetworktofusevideo, audio,
and flowfeatures.
2022 HL-Net+ [ 187] Video+ Audio Introducingcoarse-grained violent frameand fine-grained
violence detection tasks andproposingaudio-visual violence
detection network.
2022 AGAN [ 134] Video+ Audio Usingcross-modalinteractiontoenhance videoand audio
and computinghigh-confidence violence scores using
temporal convolution.
usedadeepneuralnetworktoextractvideoandaudiofeaturesandthenenhancedthefeaturesin
thetemporaldimension usinga cross-modalperceptuallocalarousalnetwork.
Wei et al. [ 182]proposedatwo-stagemultimodalinformationfusionmethod,whichfirstrefines
video-levelhardlabelsintoclip-levelsoftlabelsandthenusesanattentionmoduleformultimodal
informationfusion.Theirextensionwork, MultimodalSupervisedAttentionalAugmentation
Fusion(MSAF) [181],usedattentionfusiontoaligninformationandachievedimplicitalignment
of multimodal data.
Shangetal.[ 153]observedthatexistingmodelsarelimitedbysmalldatasetsandproposedmu-
tualdistillationtotransferinformationfromlarge-scaledatasetstosmalldatasets.Theyproposed
a multimodal attention fusion strategy to fuse RGB images, audio, and flow to obtain a more dis-
criminativerepresentation.[ 133]introducedanaudio-visualsceneclassificationtaskandreleased
amultimodaldataset.Theauthorstrydifferentdeepnetworksandfusionstrategiestoexplorethe
most effectiveclassificationmodel.
5 SUPERVISED VIDEO ANOMALY DETECTION
Supervisedvideoanomalydetectionrequiresframe-levelorpixel-levellabelstosupervisemodels
todistinguishbetweennormalandanomalies.Therefore,itisoftenconsideredaclassificationtask
ratherthanamainstreamGVAEDscheme.Ontheonehand,collectingfine-grainedlabeledanoma-
lous samples is time-consuming. On the other hand, the anomalous behavior occurs gradually,
andthedegreeofanomalyisarelativevalue,whilemanuallabelingcanonlyprovidediscrete0 /1
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:22 Y .L iue ta l.
Fig. 8. Workflow of two representative FVAD methods: (a) SDOR [ 129] and (b) GCL [ 207]. Given the un-
labeled videos, the SDOR first divided them into pseudo-normal and anomalous sets by initial anomaly
detection.GCLintroducescross-supervisiontotrainthegenerator Ganddiscriminator Dtolearnanomaly
detectors.Thepseudo-labels from GandDareusedtocomputeeach other’s losses.
labels,whichcannotadequatelydescribetheseverityandtemporalcontinuityofvideoanomalies.
Existing SVAD methods usually consider VAD a binary classification task under data imbalance
conditions. However, game engines can simulate various types of anomalous events and provide
frame-levelandpixel-levelpersonalizedannotations.Withthepenetrationofsyntheticdatasetsin
vision tasks, supervised training of GVAED models with virtual anomalies is expected to become
possible.Researchersneedtofocusonthedomainadaptationproblemposedbysyntheticdatasets,
i.e., how to cope with the covariate shifts between synthetic data and the real-world surveillance
videoandtheensuingperformancedegradation.Moreover,althoughthetrainingsetcontainspar-
tially labeled anomalies, SVAD models still need to consider how to reasonably generalize the
anomaliestodetectunseenanomalouseventsinreal-worldscenarios.SVADisanopen-setrecog-
nitiontaskratherthana supervisedbinaryclassification.
6 FULLY-UNSUPERVISED VIDEO ANOMALY DETECTION
Fully-unsupervisedVideo AnomalyDetection(FVAD) doesnotlimitthecompositionofthe
training data and requires no data annotation. In other words, FVAD tries to learn an anomaly
detectorfromtherandomrawdata,whichisa newly emerged technicalroutein recentyears.
Ionescuetal.[ 167]introducedtheunmaskingtechniquetocomputervisiontasks,proposingan
FVADframeworkthatrequiresnotrainingsequences.Theyiterativelytrainedabinaryclassifierto
distinguishtwoconsecutivevideosequencesandsimultaneouslyremovedthemostdiscriminative
features at each step. Inspired by [ 167], Liu et al. [ 94] tried to establish the connection between
heuristic unmasking and multiple classifiers two sample tests to improve its testing capability. In
this regard, they proposed a history sampling method to increase the testing power as well as to
improve the GVAED performance. Li et al. [ 83] first used a distribution clustering framework to
identify the possible anomalous samples in the training data, and then used the clustered subset
ofnormaldatatotraintheauto-encoder.Anencoderthatcandescribenormalitywasobtainedby
repeatingnormalsubsetselectionand representationlearning.
The recent representative FVAD works are Self-trained Deep Ordinal regression (SDOR)
[129]a n dGenerative Cooperative Learning (GCL) [207], which attempted to learn anomaly
scorers from unlabeled videos in an end-to-end manner, as shown in Figure 8(a) and8(b).
Specifically, SDOR [ 129] first determined the initial pseudo-normal and abnormal sets and then
computed the abnormal scores using pre-trainedResNet-50 and FCN. The representationmodule
and the scorer were optimized iteratively in a self-training manner. Moreover, Lin et al. [ 88]
looked at the pseudo label generation process in SDOR from a causal inference perspective and
proposedacausalgraphtoanalyzeconfoundingeffectsandeliminatetheimpactofnoisypseudo
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:23
Table 6. EER andAUC Comparisonof EarlyUnsupervisedMethods onBenchmarkDatasets
Year Method Ped1AUC Ped1EER Ped2AUC Ped2EER AvenueAUC AvenueEER ShanghaiTech AUC
2015 DRAM [ 190] 92.1 16.0 90.8 17.0 - - -
2015 STVP [ 3] 93.9 12.9 94.6 10.6 - - -
2016 CMAC [ 215] 85.0 - 90.0 - - - -
2016 FF-AE [ 50] 81.0 27.9 90.0 21.7 70.2 25.1 60.9
2017 DEM [ 41] 92.5 15.1 - - - - -
2017 CFS [ 73] 82.0 21.1 84.0 19.2 - - -
2017 WTA-AE [ 166] 91.9 15.9 92.8 11.2 82.1 24.2 -
2017 EBM [ 171] 70.3 35.4 86.4 16.5 78.8 27.2 -
2017 CPE [ 168] 78.2 24.0 80.7 19.0 - - -
2017 LDGK [ 54] - - 92.2 13.9 - - -
2017 sRNN [ 110] - - 92.2 - 81.7 - 68.0
2017 GANS [ 141] 97.4 8.0 93.5 14.0 - - -
2017 OGNG [ 159] 93.8 - 94.0 - - - -
2018 FFP [ 92] 83.1 - 95.4 - 85.1 - 72.8
2018 PP-CNN [ 140] 95.7 8.0 88.4 18.0 - - -
2019 FAED[ 108] 93.8 14.0 95.0 - - - -
2019 NNC [ 63] ----8 8 . 9 - -
2019 OC-AE[ 62] - - 97.8 - 90.4 - 84.9
2019 AMC [ 127] - - 96.2 - 86.9 - -
2019 MLR [ 170] 82.3 23.5 99.2 2.5 71.5 36.4 -
2019 memAE [ 46] - - 94.1 - 83.3 - 71.2
2019 MLEP [ 91] ----9 2 . 8 - 7 6 . 8
2019 BMAN [ 71] - - 96.6 - 90.0 - 76.2
2020 Street Scene [ 137] 77.3 25.9 88.3 18.9 72.0 33.0 -
2020 IPR [ 162] 82.6 - 96.2 - 83.7 - 73.0
2020 DFSN [ 138] 86.0 23.3 94.0 14.1 87.2 18.8 -
labels.Inaddition,theirproposedCILmodelimprovedsignificantlybyperformingcounterfactual
inferenceto capturelong-range temporaldependencies.
In contrast, GCL [ 207] attempted to exploit the low-frequency nature of anomalous events. It
included a generator Gand a discriminator D, which were supervised by each other in a coop-
erative manner. The generator primarily generated representations for normal events. While for
anomaly events, the generator used negative learning techniques to distort the anomaly repre-
sentation and generated pseudo-labels to train D. The discriminator estimated the probability of
anomalies and created pseudo labels to improve G. The scarcity and infrequent occurrence of
anomalies provide valuable insights into the development of FVAD. Hu et al. [ 55] leveraged the
rarityofanomalies,operatingundertheassumptionthatthesmallnumberofanomaloussamples
inthetrainingsethasalimitedimpactonthenormalityofthemodellearningprocess.Inspiredby
theMasked Auto-Encoder (MAE) [51], their proposed TMAE learned representations using a
visualtransformerperformingacomplementarytask.Notably,MAE[ 51]appliedmasksprimarily
to2Dimages,whereasvideoanomaliesarecloselylinkedtotemporalinformation.Toaddressthis
challenge, TMAE first identified video foregrounds and constructed temporal cubes to serve as
masked objects,ensuringamore comprehensiveapproachto anomaly detectionin videodata.
7 PERFORMANCE COMPARISON
We collect the performance of existing works on publicly available datasets [ 84,92,107,158,220]
to quantitatively compare the superiority and present the GVAED development progress. Table 6
presentstheframe-levelAUCandEERoftheearlyUVADmodelsonUCSDPed1&Ped2[ 84],and
CUHK Avenue [ 107] datasets and the frame-level AUC on the ShanghaiTech [ 92] dataset. Since
the recent UVAD [ 14,46,97,101] and FVAD [ 88,129,207] only report frame-level AUC as the
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:24 Y .L iue ta l.
Table 7. AUC Comparisonof Recent Unsupervisedand Fully-unsupervised (Marked inItalics) Methodson
BenchmarkDatasets
Year Method Ped2 Avenue ShanghaiTech Year Method Ped2 Avenue ShanghaiTech
2020 MNAD-R [ 131] 90.2 82.8 69.8 2020 MNAD-P [ 131] 97.0 88.5 70.5
2020 DD-GAN [ 35] 95.6 84.9 73.7 2020SDOR[129] 83.2 - -
2020 ASSAD [ 36] 97.8 86.4 71.6 2020 FSSA [ 109] 96.2 85.8 77.9
2020 VEC [ 201] 97.3 89.6 74.8 2020 Multispace [ 60] 95.4 86.8 73.6
2020 CDD-AE [ 14] 96.5 86.0 73.3 2021 CDD-AE+ [ 13] 96.7 87.1 73.7
2021 Multi-task (objectlevel) [ 43] 99.8 91.9 89.3 2021 Multi-task (frame level) [ 43] 92.4 86.9 83.5
2021 Multi-task (late fusion) [ 43] 99.8 92.8 90.2 2021 HF2AVD [105] 99.3 91.1 76.2
2021 AST-AE [ 96] 96.6 85.2 68.8 2021 ROADMAP[ 178] 96.3 88.3 76.6
2021 CT-D2GAN [ 40] 97.2 85.9 77.7 2022 AMAE [ 97] 97.4 88.2 73.6
2022 STM-AE [ 101] 98.1 89.8 73.8 2022 BiP[ 15] 97.4 86.7 73.6
2022 AR-AE [ 69] 98.3 90.3 78.1 2022 TAC-Net [ 60] 98.1 88.8 77.2
2022 STC-Net [ 218] 96.7 87.8 73.1 2022 HSNBM [ 5] 95.2 91.6 76.5
2022CIL(ResNet50)+DCFD [88] 97.9 85.9 - 2022CIL(ResNet50)+DCFD+CTCE [88] 99.4 87.3 -
2022CIL(I3D-RGB)+DCFD+CTCE [88] 98.7 90.3 - 2022GCLPT(RESNEXT) [207] - - 78.93
Table 8. QuantitativePerformance Comparisonof Weakly-supervised Methodson PublicDatasets
Method Feature UCF-Crime AUC UCF-Crime FAR ShanghaiTechAUC ShanghaiTechFAR
MIR[158]C 3 DRGB75.40 1.90 86.30 0.15
TCN [213]C 3 DRGB78.70 - 82.50 0.10
Zhong[220]C 3 DRGB80.67 3.30 76.44 -
ARNet[172]C 3 DRGB- - 85.01 0.57
I3DRGB- - 85.38 0.27
I3DRGB+Optical Flow- - 91.24 0.10
MIST[39]C 3 DRGB81.40 2.19 93.13 1.71
I3DRGB82.30 0.13 94.83 0.05
RTFM[164]C 3 DRGB83.28 - 91.51 -
I3DRGB84.30 - 97.21 -
SMR[98]I 3 DRGB+Optical Flow81.70 - - -
DTED[66]C 3 DRGB79.49 0.50 87.42 -
main evaluation metric, we have collatedthesemethods separatelyin Table 7. TheShanghaiTech
dataset was proposed in 2018 with the FFP [ 92] model, so methods before this time were usually
tested without this dataset. With the advantage of its data size and quality, ShanghaiTech has be-
comethemostwidelyusedUVADbenchmark.Aninterestingphenomenonisthattheobject-level
methods outperform other frame-level and patch-level models on the cross-scene ShanghaiTech
dataset.Forexample,theframe-levelAUCoftheMulti-task[ 43]modelisashighas90.2%,which
is 12.1% higher than the state-of-the-art frame-level methods [ 69]. It shows that for cross-scene
GVAEDtasks,usinganobjectdetectortoseparatetheforegroundobjectofinterestfromthescene
can effectively avoid interference of the background. In addition, the multi-stream model learns
normality in both temporal and spatial dimensions and generally outperforms the single-stream
model.TheusagefrequencyshowsthatUCSDPed2[ 84],CUHKAvenue[ 107],andShanghaiTech
[92] have become the prevailing benchmarks for UVAD evaluation. Future work should consider
testingandcomparingtheproposedmethodsonthesethreedatasets.
Table8presentstheperformanceofWAEDmethodsontheUCF-Crime[ 158]andShanghaiTech
Weakly [220] datasets. As mentioned previously, WAED models usually rely on pre-trained fea-
ture extractors [ 11,165,176] to obtain feature representations. Commonly used features include
C3DRGB, I3DRGB, and I3DRGB+Optical flow. The performance gap of the same model using
different features show that the effectiveness of the WAED model is related to the pre-trained
feature extractors, with the I3D outperforming the simple 3 ×3×3 convolution-based C3D
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:25
network due to the separate consideration of temporal information variation. Future WAED
work should test the performance of the proposed model on current commonly used features
or provide the performance of existing works on emerging features to demonstrate that the
performancegaincomesfromthemodeldesignratherthanbenefitingfromamorerobustfeature
extraction network. In addition to detection performance, other metrics are processing speed
and deployment cost. GVAED typically employs the Average Inference Speed (AIS) as a visual
metric to gauge the overhead cost of the model. Figures reported in existing literature often
lack direct comparability due to variations in experimental environments and computational
platforms. Moreover, recent advancements in GVAED research, such as object-level methods and
weakly-supervisedschemes,typicallyinvolveintricatedatapreprocessingandcallstopre-trained
models,suchasforegroundobjectdetection,opticalflowestimation,andspatial-temporalfeature
extractionwithwell-trained3Dconvolutionalnetworks.Itremainsunclearwhetherthecomputa-
tionalcostandprocessingtimeassociatedwiththeseaspectsarefactoredintotheoverheadcostof
the proposed model. Consequently, reporting inference speed and comparing computational cost
arenotwidespreadpracticeinGVAEDresearch.Thefewpapersprovidingsuchresultsoftenlack
a detailed description of the experimental setup. Nevertheless, we diligently collected the AIS of
existing works to offer an intuitive demonstration of the trend toward lighter-weight GVAED re-
search.Acknowledgingtheimpactofimageresolutiononmodelinferencespeed,wefollow[ 139]
to summarize these data while simultaneously documenting the dataset used for model testing.
Theresultsarepubliclyaccessiblein ourGitHub repository1and willbe continuouslyupdated.
8 CHALLENGES AND TRENDS
8.1 Research Challenges
8.1.1 Mock Anomalies vs. Real Anomalies: How to Bridge Domain Offsets between Mock and
RealAnomalies? GVAEDaimstoautomaticallydetectanomalouseventsinthelivingenvironment
to provide a safe space for humans. However, the difficulty of collecting anomalies makes most
of the existing datasets formulate abnormal events by human simulation, such as the UMN [ 28],
CUHK Avenue [ 107], and ShanghaiTech [ 92]. The mock anomalies are simpler, and their spatial-
temporalpatternsdiffersignificantlyfromnormalevents,resultinginwell-trainedmodelsdifficult
todetectcomplexanomalies.Inaddition,thesetoflimitedcategoriesofanomalouseventsconflicts
with the diverse nature of real anomalies. As a result, models learned on such datasets perform
poorly in real-world scenarios. Therefore, collecting datasets containing various real anomalies
anddesigningmodelstobridgethegapbetweenmockandrealanomaliesisanessentialchallenge
for GVAED development.
8.1.2 Single-Scenevs.Multi-Scenes:HowtoDevelopCross-ScenarioGVAEDModelsfortheReal
World?Mainstream unsupervised datasets [ 84,107] and UVAD methods [ 14,92,131] only con-
sidersingle-scenevideos,whiletherealworldalwayscontainsmultiplescenes,whichconstitutes
another challenge for UVAD methods. Although the UMN [ 28] and ShanghaiTech [ 92] datasets
include multiple scenes, the anomalous events of the former are all crowd dispersal, while the 13
scenesofthelatteraresimilar.Therefore,mostUVADmethods[ 92,101]donotconsiderthescene
differences but learn normality directly from the original video as in other single-scene datasets
[84,107]. Recent researchers [ 44,62] believe that object-level methods are a feasible way to learn
scene-invariant normality by extracting specific objects from the scenes and then analyzing the
spatial-temporal patterns of objects and backgrounds separately. The multi-scene problem is in-
escapableformodeldeploymentasitisalmostimpossibletotrainascene-specificmodelforeach
terminal device. Developing cross-scene GVAED models using domain adaptation/generalization
techniques[ 90,183,189] to learnscene-invariantnormality is adefinitechallenge.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:26 Y .L iue ta l.
8.1.3 RealDatavs.SyntheticData:HowtoDevelopLargeFine-GrainedGVAEDModelsusingSyn-
theticData? Duetotherarityanddiversityofanomalies,collectingandlabelinganomalousevents
is time-consuming and laborious. Therefore, researchers [ 1] have considered using game engines
[38,152] to synthesize anomaly data. We remain optimistic about this attempt and believe it may
lead to new research opportunities for GVAED. While anomaly detection tasks suffer from a lack
of data and missing labels. Synthetic data can generate various anomalous samples and provide
precise frame-level or even pixel-level annotations, making it possible to develop SVAD models
and save data preparation costs for large-scale GVAED model training. However, a concomitant
challenge is that covariate shifts between synthetic and real data may make the trained GVAED
modelsnot workin realscenes.
8.1.4 Unimodal vs. Multimodal: How to Effectively Fuse Multimodal Data to Mine Anomaly-
Related Clues? Researchers [ 186,187] are aware of the positive impact of multimodal data (e.g.,
audio) for GVAED. However, existing works are stuck on the lack of datasets and the validity of
model structures. XD-Violence [ 186] is the only mainstream multimodal GVAED dataset, but it
onlycontainsvideoandaudio,andmuchdataiscollectedfrommoviesandgamesratherthanthe
real world. With the popularity of IoT, using various sensors to collect environmental informa-
tion(e.g.,temperature,brightness,andhumidity)canassistcamerasindetectingabnormalevents.
However,miningusefulcluesfromvaliddataanddevelopingefficientmultimodalGVAEDmodels
needfurtherresearch,suchastask-specificfeatureextractionfromheterogeneousdata,semantic
alignment of different modalities, anomaly-related multimodal information fusion, and domain
offsetbridgingin emergingcross-modalGVAEDresearch.
8.1.5 Single-Viewvs.Multi-View:HowtoIntegrateComplementaryInformationfromMulti-View
Data?Inplacessuchastrafficintersectionsandparks,thesameareaisusuallycoveredbymultiple
cameraviews,derivinganothertask:anomalouseventdetectioninmulti-viewvideos.Multi-view
data can provide more comprehensive environmental awareness data, which is wildly used for
re-identification[ 87,199],tracking[ 163]andgazeestimation[ 86].However,existingdatasets[ 28,
84,92,107]areallsingle-view,makingmulti-viewGVAEDresearchstillagap.Thesimplestideais
tocombinedatafromallviewstotrainthesamemodelanddetermineanomaliesthroughavoting
or winner-take-all strategy. However, such approaches are training-costly and do not consider
the differences and complementarities between multi-view data. Therefore, multi-view GVAED
remainsto beinvestigated.
8.1.6 Offline vs. Online Detection: How to Develop Light-Weight End-to-End Online GVAED
Models?Thedeployable Intelligentvideosurveillancesystems(IVSS) needtoprocesscontin-
uouslygeneratedvideostreams24/7onlineandrespondtoanomalouseventsinreal-timesothat
noteworthy clips can be saved in time to reduce storage and transmission costs. Unfortunately,
existing GVAED models are designed for public datasets rather than real-time video streams,
primarily pursuing detection performance while avoiding the online detection challenges. For
example, the dominant prediction-based methods [ 92,97,101,131] in UVAD route can only give
thepredictionerrorofthecurrentinputinasingle-stepexecution,whiletheInformativeanomaly
score needs are obtained after performing the maximum-minimum normalization over the
predictionerrorofallframes.Althoughthemodelcandirectlydeterminethecurrentframeasan
anomalywithapre-seterrorthreshold,existingattemptsshowthatthemanuallyselectedthresh-
old is unreliable. WAED [ 39,98,115,158,164] can directly output anomaly scores for segments.
However, the input to the scoring module is a discriminative spatial-temporal representation
rather than the original video. The representations usually rely on pre-trained 3D convolution-
basedfeatureextractors[ 11,165,176].Thetimecostisunacceptableforresource-limitedterminal
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:27
devices [65]. Therefore, developing online detection models is the primary challenge for GVAED
deployment, determiningitsapplicationpotentialin IVSSandstreamingmedia platforms.
8.2 Development Trends
8.2.1 Data Level: Toward Real-World GVAED Model Development for Multi-View Cross-Scene
MultimodalData. Fromsingle-scene[ 84,107]tomulti-scene[ 92,137],fromreal-wordvideos[ 28]
to synthetic data [ 1], and from unimodal [ 158] to multimodal [ 186], GVAED datasets are moving
towards large-scale and realistic scenarios. We see this as a positive trend that will continue
with the growth of online video platforms and tools. On the one hand, real-world scenarios
and anomalies are diverse, so efficient models for real-world applications need to be trained on
large-scale datasets that contain various anomalous events. On the other hand, the Internet has
madeitpossibletocollectmulti-sceneandmulti-viewvideos,includingsufficientrareanomalous
behaviors such as violence and crime. Furthermore, multimodal and synthetic data will be
increasingly important in GVAED research. The XD-Violence [ 186] dataset has demonstrated the
positive impact of multimodal data on GVAED. In the future, with streaming media (e.g., TikTok,
Netflix, and Hulu) and online video sites (e.g., YouTube, iQIYI, and Youku), more modal data can
be collected. Besides, virtual game engines (e.g., Airsim [ 152]a n dC a r l a[ 38]) can synthesize rare
anomalous events and provide fine-grained annotations on demand. The connection of GVAED
withothertasks(e.g.,multimodalanalysis[ 181]andfew-shotlearning[ 109])willtendtobeclose,
withthelatterinspiringthedesign of GVAED modelsunder specificdata conditions.
8.2.2 Representation Level: Adaptive Transfer of Emerging Representation Learning and Feature
Extraction Means. Deep learning has enabled spatial-temporal representations to be derived di-
rectlyfromtherawvideosinanend-to-endmannerwithoutahumanprior[ 179].Theearlierdeep
GVAEDmodelsbenefitfromCNNsandpursuecomplexdeepnetworkstoextractmoreabstractfea-
tures. For example, the UVAD models attempt to introduce dual-stream networks to learn spatial
and temporal representations [ 14,97,101], and use 3D convolutional networks to model tempo-
ral features [ 46,219]. From C3D [ 165] to I3D [ 11], the WAED models [ 39,98,164] benefit from
morepowerfulpre-trainedfeatureextractorsandachievesgeneralperformancegainsonexisting
datasets[ 158,220].WeobservethattherepresentationmeansofWAEDwillbecomeincreasingly
sophisticated.NewvisualrepresentationlearningmodelssuchasTransformer[ 4,57,81,169]will
drive WAED development. In contrast, UVAD does not pursue abstract representations. Overly
powerful deep networks may lead to missing anomalous events as normal due to overgeneral-
ization [46,131]. Future researchers should consider using clever representation strategies (e.g.,
causal representation learning [ 103]) to balance the model’s powerful representation of normal
events and the limited generalization of abnormal events [ 99]. Powerful generative models such
as graph learning [ 58,111,120] and diffusion models [ 27] are expected to provide more effective
normalitylearningtoolsforUVAD.Inaddition,researchersshouldconsiderintroducingemerging
techniques(e.g.,domainadaptation[ 44,174]andknowledgedistillation[ 180])todevelopGVAED
models for learningscene-invariantrepresentationfrom multi-sceneand multi-view videos.
8.2.3 Deployment Level: Lightweight Easy-to-Deploy Model Development for Resource-
Constrained End Devices. Model deployment is an inevitable trend for GVAED development. As
mentioned above, the multi-scene and the diversity of anomalies in real-world videos pose new
challenges for model design and training, such as online detection, lightweight models, and high
view robustness. On the one hand, the computational resources of terminal devices are limited.
Most deep GVAED methods are overly pursuing performance at the expense of running costs.
On the other hand, existing models are trained offline, which cannot perform real-time detection.
Modelcompression[ 33]andknowledgedistillation[ 47]candrivethedevelopmentoflightweight
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:28 Y .L iue ta l.
GVAEDmodels.Onlineevolutivelearning[ 76,77],edge-cloudcollaboration,andintegratedsens-
ingandcontroltechnologies[ 135]enablemodelstodynamicallyoptimizelearnableparametersin
complexenvironmentssuchasmodernindustry[ 99]andintelligenttransportationsystems[ 194].
8.2.4 Methodology Level: High-Efficiency & Robust GVAED Development by Integrating Dif-
ferent Research Pathways. This survey compares the four main GVAED technical routes: UVAD,
WAED, SVAD, and FVAD. UVAD has been regarded as the mainstream solution, although
WAED gradually dominates in recent years. However, the trend of UVAD is unclear due to its
performance saturation on limited datasets [ 84]. In addition, the setting of anomalies in UVAD
datasets makes UVAD models challenging to work in complex scenes. Self-supervised visual
representation technicals (e.g., contrast learning [ 18,48,52,60] and deep clustering [ 10,210])
may provide new ideas for UVAD. In contrast, WAED has been widely noticed as a research
hotspot due to its excellent performance in crime detection [ 158]. In addition, the multimodal
videoanomalydetectiontasksalsofollowWAEDroutes.SVADisonceabandonedduetothelack
of labels and anomalies. However, it may face new research opportunities with the emergence
of synthetic datasets [ 1]. In contrast, FVAD can learn directly from raw video data without
the cost of training data filtering and annotations, making it a hot research topic. The various
routesarenotcompletelyindependent,andexistingworks[ 100,184]havestartedtocombinethe
assumptionsof differentmethodstodevelop more efficientGVAEDmodels.
9 CONCLUSION
This survey is the first to integrate the deep learning-driven technical routes based on different
assumptions and learning frameworks into a unified generalized video anomaly event detection
framework. We provide a hierarchical GVAED taxonomy that systematically organizes the
existing literature by supervision, input data, and network structure, focusing on the recent
advances such as weakly-supervised, fully-unsupervised, and multimodal methods. To provide
a comprehensive survey of the extant work, we collect benchmark datasets and available codes,
sort out the development lines of various methods, and perform performance comparisons and
strengths analysis. This survey helps clarify the connections among deep GVAED routes and
advance community development. In addition, we analyze research challenges and future trends
in the context of deep learning technology development and possible problems faced by GAED
modeldeployment,whichcan serveasa guidefor futureresearchersandengineers.
REFERENCES
[1] Andra Acsintoae, Andrei Florescu, Mariana-Iuliana Georgescu, Tudor Mare, Paul Sumedrea, Radu Tudor Ionescu,
FahadShahbazKhan,andMubarakShah.2022.UBnormal:Newbenchmarkforsupervisedopen-setvideoanomaly
detection. In Proceedings oftheIEEE/CVFConferenceonComputer Visionand PatternRecognition . 20143–20153.
[2] Amit Adam, Ehud Rivlin, Ilan Shimshoni, and Daviv Reinitz. 2008. Robust real-time unusual event detection using
multiplefixed-locationmonitors. IEEETransactionsonPatternAnalysisandMachineIntelligence 30,3(2008),555–560.
[3] Borislav Antić and Björn Ommer. 2015. Spatio-temporal video parsing for abnormality detection. arXiv preprint
arXiv:1502.06235 (2015).
[4] Anurag Arnab, Mostafa Dehghani, Georg Heigold, Chen Sun, Mario Lučić, and Cordelia Schmid. 2021. ViViT: A
video vision transformer. In Proceedings oftheIEEE/CVFInternational ConferenceonComputerVision .6836–6846.
[5] Qianyue Bao, Fang Liu, Yang Liu, LichengJiao, Xu Liu, and LinglingLi.2022. Hierarchical scene normality-binding
modeling for anomaly detection in surveillance videos. In Proceedings of the 30th ACM International Conference on
Multimedia . 6103–6112.
[6] YannickBenezeth,P.-M.Jodoin,VenkateshSaligrama,andChristopheRosenberger.2009.Abnormaleventsdetection
based on spatio-temporal co-occurences. In 2009 IEEE Conference on Computer Vision and Pattern Recognition . IEEE,
2458–2465.
[7] AneBlázquez-García,AngelConde,UsueMori,andJoseA.Lozano.2021.Areviewonoutlier/anomalydetectionin
timeseries data. ACMComputing Surveys (CSUR) 54,3(2021),1–33.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:29
[8] Ruichu Cai, Hao Zhang, Wen Liu, Shenghua Gao, and Zhifeng Hao. 2021. Appearance-motion memory consistency
networkforvideoanomalydetection.In ProceedingsoftheAAAIConferenceonArtificialIntelligence ,Vol.35.938–946.
[9] Yiheng Cai, Jiaqi Liu, Yajun Guo, Shaobin Hu, and Shinan Lang. 2021. Video anomaly detection with multi-scale
featureandtemporalinformationfusion. Neurocomputing 423(2021),264–273.
[10] MathildeCaron, IshanMisra,JulienMairal,PriyaGoyal,Piotr Bojanowski,andArmandJoulin.2020.Unsupervised
learningofvisualfeaturesbycontrastingclusterassignments. AdvancesinNeuralInformationProcessingSystems 33
(2020),9912–9924.
[11] JoaoCarreiraandAndrewZisserman.2017.Quovadis,actionrecognition?Anewmodelandthekineticsdataset.In
Proceedings oftheIEEEConferenceonComputer VisionandPattern Recognition . 6299–6308.
[12] S. Chandrakala, K. Deepak, and G. Revathy. 2022. Anomaly detection in surveillance videos: A thematic taxonomy
of deep models, reviewandperformanceanalysis. Artificial Intelligence Review (2022),1–50.
[13] YunpengChang,ZhigangTu,WeiXie,BinLuo,Shifu Zhang,HaigangSui,andJunsong Yuan.2021.Video anomaly
detectionwith spatio-temporaldissociation. Pattern Recognition 122(2021),108213.
[14] YunpengChang,ZhigangTu,WeiXie,andJunsongYuan.2020.Clusteringdrivendeepautoencoderforvideoanom-
alydetection.In EuropeanConference onComputerVision .Springer, 329–345.
[15] Chengwei Chen, Yuan Xie, Shaohui Lin, Angela Yao, Guannan Jiang, Wei Zhang, Yanyun Qu, Ruizhi Qiao, Bo Ren,
and Lizhuang Ma. 2022. Comprehensive regularization in a bi-directional predictive network for video anomaly
detection.In Proceedings of theAmerican Association forArtificial Intelligence .1– 9.
[16] Dongyue Chen, Pengtao Wang, Lingyi Yue, Yuxin Zhang, and Tong Jia. 2020. Anomaly detection in surveillance
video basedon bidirectionalprediction. Image andVision Computing 98(2020),103915.
[17] Dongyue Chen, Lingyi Yue, Xingya Chang, Ming Xu, and Tong Jia. 2021. NM-GAN: Noise-modulated generative
adversarialnetwork for video anomalydetection. Pattern Recognition 116(2021),107969.
[18] Ting Chen, Simon Kornblith, Mohammad Norouzi, and Geoffrey Hinton. 2020. A simple framework for contrastive
learningof visual representations.In International ConferenceonMachine Learning . PMLR,1597–1607.
[19] Weiling Chen, Keng Teck Ma, Zi Jian Yew, Minhoe Hur, and David Aik-Aun Khoo. 2023. TEVAD: Improved video
anomaly detection with captions. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recog-
nition.5548–5558.
[20] Zhaoyu Chen, Bo Li, Jianghe Xu, Shuang Wu, Shouhong Ding, and Wenqiang Zhang. 2022. Towards practical cer-
tifiable patch defense with vision transformer. In Proceedings of the IEEE/CVF Conference on Computer Vision and
Pattern Recognition . 15148–15158.
[21] KaiCheng,YangLiu,andXinhuaZeng.2023.Learninggraphenhancedspatial-temporalcoherenceforvideoanom-
alydetection. IEEESignal Processing Letters 30(2023),314–318.
[22] Kai Cheng, Xinhua Zeng, Yang Liu, Mengyang Zhao, Chengxin Pang, and Xing Hu. 2023. Spatial-temporal graph
convolutional network boosted flow-frame prediction for video anomaly detection. In ICASSP 2023-2023 IEEE Inter-
national ConferenceonAcoustics, Speech andSignal Processing (ICASSP) .IEEE, 1–5.
[23] Kai-Wen Cheng, Yie-Tarng Chen, and Wen-Hsien Fang. 2015. Video anomaly detection and localization using hier-
archical feature representation and Gaussian process regression. In Proceedings of the IEEE Conference on Computer
Vision and Pattern Recognition . 2909–2917.
[24] YongSheanChongandYongHaurTay.2017.Abnormaleventdetectioninvideosusingspatiotemporalautoencoder.
InInternational Symposium on Neural Networks . Springer,189–196.
[25] Peter Christiansen, Lars N. Nielsen, Kim A. Steen, Rasmus N. Jørgensen, and Henrik Karstoft. 2016. DeepAnomaly:
Combiningbackgroundsubtractionanddeeplearningfordetectingobstaclesandanomaliesinanagriculturalfield.
Sensors16,11(2016),1904.
[26] Andrew A. Cook, Göksel Mısırlı, and Zhong Fan. 2019. Anomaly detection for IoT time-series data: A survey. IEEE
InternetofThings Journal 7,7(2019),6481–6494.
[27] Florinel-Alin Croitoru, Vlad Hondru, Radu Tudor Ionescu, and Mubarak Shah. 2022. Diffusion models in vision: A
survey.arXiv preprint arXiv:2209.04747 (2022).
[28] XinyiCui,QingshanLiu,MingchenGao,andDimitrisN.Metaxas.2011.Abnormaldetectionusinginteractionenergy
potentials.In CVPR2011 . IEEE, 3161–3167.
[29] Navneet Dalal and Bill Triggs. 2005. Histograms of oriented gradients for human detection. In 2005 IEEE Computer
Society Conferenceon Computer Vision andPattern Recognition (CVPR’05) ,Vol. 1.IEEE, 886–893.
[30] Navneet Dalal, Bill Triggs, and Cordelia Schmid. 2006. Human detection using oriented histograms of flow and
appearance.In European ConferenceonComputer Vision .Springer, 428–441.
[31] K. Deepak, S. Chandrakala, and C. Krishna Mohan. 2021. Residual spatiotemporal autoencoder for unsupervised
video anomalydetection. Signal,Image and VideoProcessing 15,1(2021),215–222.
[32] Hanqiu Deng, Zhaoxiang Zhang, Shihao Zou, and Xingyu Li. 2023. Bi-directional frame interpolation for unsuper-
visedvideoanomalydetection.In ProceedingsoftheIEEE/CVFWinterConferenceonApplicationsofComputerVision .
2634–2643.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:30 Y .L iue ta l.
[33] Lei Deng, Guoqi Li, Song Han, Luping Shi, and Yuan Xie. 2020. Model compression and hardware acceleration for
neural networks: A comprehensivesurvey. Proc. IEEE 108,4(2020),485–532.
[34] Piotr Dollár, Vincent Rabaud, Garrison Cottrell, and Serge Belongie. 2005. Behavior recognition via sparse spatio-
temporalfeatures.In 2005IEEEInternationalWorkshoponVisualSurveillanceandPerformanceEvaluationofTracking
and Surveillance . IEEE, 65–72.
[35] Fei Dong, Yu Zhang, and Xiushan Nie. 2020. Dual discriminator generative adversarial network for video anomaly
detection. IEEEAccess 8(2020),88170–88176.
[36] KevalDoshiandYasinYilmaz.2020.Any-shotsequentialanomalydetectioninsurveillancevideos.In Proceedingsof
theIEEE/CVFConferenceonComputerVision andPattern Recognition Workshops .934–935.
[37] KevalDoshiandYasinYilmaz.2021.Onlineanomalydetectioninsurveillancevideoswithasymptoticboundonfalse
alarmrate. Pattern Recognition 114(2021),107865.
[38] AlexeyDosovitskiy,GermanRos,FelipeCodevilla,AntonioLopez,andVladlenKoltun.2017.CARLA:Anopenurban
driving simulator.In ConferenceonRobotLearning . PMLR,1–16.
[39] Jia-ChangFeng,Fa-TingHong,andWei-ShiZheng.2021.MIST:Multipleinstanceself-trainingframeworkforvideo
anomaly detection. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition . 14009–
14018.
[40] XinyangFeng,DongjinSong,YuncongChen,ZhengzhangChen,JingchaoNi,andHaifengChen.2021.Convolutional
transformer based dual discriminator generative adversarial networks for video anomaly detection. In Proceedings
of the29th ACMInternational Conference onMultimedia . 5546–5554.
[41] Yachuang Feng, Yuan Yuan, and Xiaoqiang Lu. 2017. Learning deep event models for crowd anomaly detection.
Neurocomputing 219(2017),548–556.
[42] Félix Fuentes-Hurtado, Abdolrahim Kadkhodamohammadi, Evangello Flouty, Santiago Barbarisi, Imanol Luengo,
and Danail Stoyanov. 2019. EasyLabels: Weak labels for scene segmentation in laparoscopic videos. International
Journal of ComputerAssistedRadiology and Surgery 14,7(2019),1247–1257.
[43] Mariana-Iuliana Georgescu, Antonio Barbalau, Radu Tudor Ionescu, Fahad Shahbaz Khan, Marius Popescu, and
Mubarak Shah. 2021. Anomaly detection in video via self-supervised and multi-task learning. In Proceedings of the
IEEE/CVFConferenceonComputer VisionandPattern Recognition . 12742–12752.
[44] MarianaIulianaGeorgescu,RaduTudorIonescu,FahadShahbazKhan,MariusPopescu,andMubarakShah.2021.A
background-agnosticframeworkwithadversarialtrainingforabnormaleventdetectioninvideo. IEEETransactions
on Pattern Analysisand Machine Intelligence 44,9(2021),4505–4523.
[45] RossGirshick.2015.FastR-CNN.In ProceedingsoftheIEEEInternationalConferenceonComputerVision .1440–1448.
[46] DongGong,LingqiaoLiu,VuongLe,BudhadityaSaha,MoussaRedaMansour,SvethaVenkatesh,andAntonvanden
Hengel. 2019. Memorizing normality to detect anomaly: Memory-augmented deep autoencoder for unsupervised
anomalydetection.In Proceedings oftheIEEE/CVFInternational ConferenceonComputer Vision .1705–1714.
[47] JianpingGou,BaoshengYu,StephenJ.Maybank,andDachengTao.2021.Knowledgedistillation:Asurvey. Interna-
tional Journal ofComputer Vision 129,6 (2021),1789–1819.
[48] Jean-Bastien Grill, Florian Strub, Florent Altché, Corentin Tallec, Pierre Richemond, Elena Buchatskaya, Carl Doer-
sch,BernardoAvilaPires,ZhaohanGuo,MohammadGheshlaghiAzar,BilalPiot,KorayKavukcuoglu,RemiMunos,
and Michal Valko. 2020. Bootstrap your own latent-a new approach to self-supervised learning. Advances in Neural
Information Processing Systems 33,(2020),21271–21284.
[49] H.Haberfehlner,A.I.Buizer,K.L.Stolk,S.S.vandeVen,I.Aleo,L.A.Bonouvrié,J.Harlaar,andM.M.vanderKrogt.
2020.Automaticvideo tracking usingdeep learningin dyskinetic cerebralpalsy. Gait Posture 81(2020),132–133.
[50] MahmudulHasan,JonghyunChoi,JanNeumann,AmitK.Roy-Chowdhury,andLarryS.Davis.2016.Learningtem-
poralregularityinvideosequences.In ProceedingsoftheIEEEConferenceonComputerVisionandPatternRecognition .
733–742.
[51] Kaiming He, Xinlei Chen, Saining Xie, Yanghao Li, Piotr Dollár, and Ross Girshick. 2022. Masked autoencoders
are scalable vision learners. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition .
16000–16009.
[52] Kaiming He, Haoqi Fan, Yuxin Wu, Saining Xie, and Ross Girshick. 2020. Momentum contrast for unsupervised
visualrepresentationlearning.In ProceedingsoftheIEEE/CVFConferenceonComputerVisionandPatternRecognition .
9729–9738.
[53] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. 2016. Deep residual learning for image recognition. In
Proceedings oftheIEEEConferenceonComputer Visionand PatternRecognition . 770–778.
[54] Ryota Hinami, Tao Mei, and Shin’ichi Satoh. 2017. Joint detection and recounting of abnormal events by learning
deep generic knowledge.In Proceedings oftheIEEEInternational ConferenceonComputer Vision .3619–3627.
[55] JingtaoHu,GuangYu,SiqiWang,EnZhu,ZhipingCai,andXinzhongZhu.2022.Detectinganomalouseventsfrom
unlabeledvideosviatemporalmaskedauto-encoding.In 2022IEEEInternationalConferenceonMultimediaandExpo
(ICME).IEEE, 1–6.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:31
[56] Xing Hu, Yingping Huang, Xiumin Gao, Lingkun Luo, and Qianqian Duan. 2018. Squirrel-cage local binary pattern
and its application in video anomaly detection. IEEE Transactions on Information Forensics and Security 14, 4 (2018),
1007–1022.
[57] Chao Huang, Chengliang Liu, Jie Wen, Lian Wu, Yong Xu, Qiuping Jiang, and Yaowei Wang. 2022. Weakly super-
visedvideoanomalydetectionviaself-guidedtemporaldiscriminativetransformer. IEEETransactionsonCybernetics
(2022).
[58] ChaoHuang,YaboLiu,ZhengZhang,ChengliangLiu,JieWen,YongXu,andYaoweiWang.2022.Hierarchicalgraph
embeddedposeregularitylearningviaspatio-temporaltransformerforabnormalbehaviordetection.In Proceedings
ofthe30th ACMInternational ConferenceonMultimedia . 307–315.
[59] Chao Huang, Jie Wen, Yong Xu, Qiuping Jiang, Jian Yang, Yaowei Wang, and David Zhang. 2022. Self-supervised
attentive generative adversarial networks for video anomaly detection. IEEE Transactions on Neural Networks and
Learning Systems (2022).
[60] ChaoHuang,ZhihaoWu,JieWen,YongXu,QiupingJiang,andYaoweiWang.2021.Abnormaleventdetectionusing
deep contrastive learning for intelligent video surveillance system. IEEE Transactions on Industrial Informatics 18, 8
(2021),5171–5179.
[61] Chao Huang, Zehua Yang, Jie Wen, Yong Xu, Qiuping Jiang, Jian Yang, and Yaowei Wang. 2021. Self-supervision-
augmented deep autoencoder for unsupervised visual anomaly detection. IEEE Transactions on Cybernetics 52, 12
(2021),13834–13847.
[62] Radu Tudor Ionescu, Fahad Shahbaz Khan, Mariana-Iuliana Georgescu, and Ling Shao. 2019. Object-centric auto-
encodersanddummyanomaliesforabnormaleventdetectioninvideo.In ProceedingsoftheIEEE/CVFConferenceon
Computer Vision andPattern Recognition . 7842–7851.
[63] Radu Tudor Ionescu, Sorina Smeureanu, Marius Popescu, and Bogdan Alexe. 2019. Detecting abnormal events in
videousingnarrowednormalityclusters.In 2019IEEEWinterConferenceonApplicationsofComputerVision(WACV) .
IEEE, 1951–1960.
[64] Sabah Abdulazeez Jebur, Khalid A. Hussein, Haider Kadhim Hoomod, Laith Alzubaidi, and José Santamaría. 2022.
Reviewon deep learningapproachesfor anomalyeventdetectionin video surveillance. Electronics 12,1(2022),29.
[65] Bobo Ju, Yang Liu, Liang Song, Guixiang Gan, Zengwen Li, and Linhua Jiang. 2023. A high-reliability edge-side
mobile terminal shared computing architecture based on task triple-stage full-cycle monitoring. IEEE Internet of
Things Journal (2023).
[66] AmmarMansoorKamoona,AmiraliKhodadadianGostar,AlirezaBab-Hadiashar,andRezaHoseinnezhad.2023.Mul-
tipleinstance-basedvideoanomalydetectionusingdeeptemporalencoding–decoding. ExpertSystemswithApplica-
tions214(2023),119079.
[67] B. Ravi Kiran, Dilip Mathew Thomas, and Ranjith Parakkal. 2018. An overview of deep learning based methods for
unsupervised andsemi-supervised anomalydetectionin videos. Journal ofImaging 4,2 (2018),36.
[68] Kwang-EunKoandKwee-BoSim.2018.Deepconvolutionalframeworkforabnormalbehaviordetectioninasmart
surveillance system. Engineering Applications ofArtificial Intelligence 67(2018),226–234.
[69] Viet-Tuan Le and Yong-Guk Kim. 2023. Attention-based residual autoencoder for video anomaly detection. Applied
Intelligence 53,3(2023),3240–3254.
[70] Jooyeon Lee, Woo-Jeoung Nam, and Seong-Whan Lee. 2022. Multi-contextual predictions with vision transformer
for video anomalydetection.In 2022 26th International Conferenceon Pattern Recognition (ICPR) .IEEE, 1012–1018.
[71] Sangmin Lee, Hak Gu Kim, and Yong Man Ro. 2019. BMAN: Bidirectional multi-scale aggregation networks for
abnormaleventdetection. IEEETransactions onImage Processing 29(2019),2395–2408.
[72] Roberto Leyva, Victor Sanchez, and Chang-Tsun Li. 2017. The LV dataset: A realistic surveillance video dataset for
abnormaleventdetection.In 2017 5th International Workshopon Biometrics andForensics (IWBF) . IEEE, 1–6.
[73] Roberto Leyva, Victor Sanchez, and Chang-Tsun Li. 2017. Video anomaly detection with compact feature sets for
onlineperformance. IEEETransactions onImage Processing 26,7(2017),3463–3478.
[74] Di Li, Yang Liu, and Liang Song. 2022. Adaptive weighted losses with distribution approximation for efficient
consistency-based semi-supervised learning. IEEE Transactions on Circuits and Systems for Video Technology 32, 11
(2022),7832–7842.
[75] DaohengLi,XiushanNie,XiaofengLi,YuZhang,andYilongYin.2022.Context-relatedvideoanomalydetectionvia
generativeadversarialnetwork. Pattern Recognition Letters 156(2022),183–189.
[76] Di Li and Liang Song. 2022. Multi-agent multi-view collaborative perception based on semi-supervised online evo-
lutivelearning. Sensors22,18(2022),6893.
[77] Di Li, Xiaoguang Zhu, and Liang Song. 2022. Mutual match for semi-supervised online evolutive learning. Applied
Intelligence (2022),1–15.
[78] Nanjun Li, Faliang Chang, and Chunsheng Liu. 2020. Spatial-temporal cascade autoencoder for video anomaly de-
tectionin crowdedscenes. IEEETransactions onMultimedia 23(2020),203–215.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:32 Y .L iue ta l.
[79] Nannan Li, Jia-Xing Zhong, Xiujun Shu, and Huiwen Guo. 2022. Weakly-supervised anomaly detection in video
surveillance viagraph convolutionallabelnoise cleaning. Neurocomputing 481(2022),154–167.
[80] WaseemUllah,TanveerHussain,ZulfiqarAhmadKhan,UmairHaroon,andSungWookBaik.2022.Intelligentdual
stream CNN andecho statenetwork for anomalydetection. Knowledge-Based Systems 253(2022),109456.
[81] ShuoLi,FangLiu,andLichengJiao.2022.Self-trainingmulti-sequencelearningwithtransformerforweaklysuper-
vised video anomalydetection. Proceedings oftheAAAI,Virtual 24(2022).
[82] Tong Li, Xinyue Chen, Fushun Zhu, Zhengyu Zhang, and Hua Yan. 2021. Two-stream deep spatial-temporal auto-
encoder for surveillance video abnormaleventdetection. Neurocomputing 439(2021),256–270.
[83] TangqingLi,ZhengWang,SiyingLiu,andWen-YanLin.2021.Deepunsupervisedanomalydetection.In Proceedings
of theIEEE/CVFWinter ConferenceonApplications ofComputer Vision .3636–3645.
[84] Weixin Li, Vijay Mahadevan, and Nuno Vasconcelos. 2013. Anomaly detection and localization in crowded scenes.
IEEETransactions onPatternAnalysis andMachine Intelligence 36,1(2013),18–32.
[85] Yuanyuan Li, Yiheng Cai, Jiaqi Liu, Shinan Lang, and Xinfeng Zhang. 2019. Spatio-temporal unity networking for
video anomalydetection. IEEEAccess 7(2019),172425–172432. https://doi.org/10.1109/ACCESS.2019.2954540
[86] Dongze Lian, Lina Hu, Weixin Luo, Yanyu Xu, Lixin Duan, Jingyi Yu, and Shenghua Gao. 2018. Multiview multi-
task gaze estimation with deep convolutional neural networks. IEEE Transactions on Neural Networks and Learning
Systems30,10(2018),3010–3023.
[87] Weipeng Lin, Yidong Li, Xiaoliang Yang, Peixi Peng, and Junliang Xing. 2019. Multi-view learning for vehicle re-
identification.In 2019 IEEEInternational Conference onMultimedia andExpo(ICME) .IEEE, 832–837.
[88] XiangruLin,YuyangChen,GuanbinLi,andYizhouYu.2022.Acausalinferencelookatunsupervisedvideoanomaly
detection. In Thirty-Sixth AAAIConference onArtificial Intelligence .1620–1629.
[89] Jing Liu, Yang Liu, Di Li, Hanqi Wang, Xiaohong Huang, and Liang Song. 2023. DSDCLA: Driving style detection
viahybrid CNN-LSTM withmulti-levelattentionfusion. AppliedIntelligence (2023),1–18.
[90] Jing Liu, Yang Liu, Wei Zhu, Xiaoguang Zhu, and Liang Song. 2023. Distributional and spatial-temporal robust rep-
resentation learningfortransportation activityrecognition. Pattern Recognition 140(2023),109568.
[91] WenLiu,WeixinLuo,ZhengxinLi,PeilinZhao,andShenghuaGao.2019.Marginlearningembeddedpredictionfor
video anomalydetectionwithA fewanomalies.In IJCAI.3023–3030.
[92] WenLiu,WeixinLuo,DongzeLian,andShenghuaGao.2018.Futureframepredictionforanomalydetection–Anew
baseline.In Proceedings oftheIEEEConferenceonComputer VisionandPattern Recognition . 6536–6545.
[93] Yang Liu, Zhengliang Guo, Jing Liu, Chengfang Li, and Liang Song. 2023. OSIN: Object-centric scene inference net-
work forunsupervised video anomalydetection. IEEESignal Processing Letters 30(2023),359–363.
[94] Yusha Liu, Chun-Liang Li, and Barnabás Póczos. 2018. Classifier two sample test for video anomaly detections. In
BMVC.71.
[95] YangLiu,DiLi,WeiZhu,DingkangYang,JingLiu,andLiangSong.2023.MSN-net:Multi-scalenormalitynetworkfor
videoanomalydetection.In ICASSP2023-2023IEEEInternationalConferenceonAcoustics,SpeechandSignalProcessing
(ICASSP).IEEE, 1–5.
[96] Yang Liu, Shuang Li, Jing Liu, Hao Yang, Mengyang Zhao, Xinhua Zeng, Wei Ni, and Liang Song. 2021. Learning
attention augmented spatial-temporal normality for video anomaly detection. In 2021 3rd International Symposium
onSmart andHealthyCities (ISHC) .IEEE, 137–144.
[97] Yang Liu, Jing Liu, Jieyu Lin, Mengyang Zhao, and Liang Song. 2022. Appearance-motion united auto-encoder
framework for video anomaly detection. IEEE Transactions on Circuits and Systems II: Express Briefs 69, 5 (2022),
2498–2502.
[98] YangLiu,JingLiu,WeiNi,andLiangSong.2022.Abnormaleventdetectionwithself-guidingmulti-instanceranking
framework.In 2022 International JointConferenceonNeural Networks (IJCNN) .IEEE, 01–07.
[99] Yang Liu, Jing Liu, Kun Yang, Bobo Ju, Siao Liu, Yuzheng Wang, Dingkang Yang, Peng Sun, and Liang Song. 2023.
AMP-Net: Appearance-motion prototype network assisted automatic video anomaly detection system. IEEE Trans-
actions on Industrial Informatics (2023),1–13.
[100] Yang Liu, Jing Liu, Mengyang Zhao, Shuang Li, and Liang Song. 2022. Collaborative normality learning framework
for weakly supervised video anomaly detection. IEEE Transactions on Circuits and Systems II: Express Briefs 69, 5
(2022),2508–2512.
[101] Yang Liu, Jing Liu, Mengyang Zhao, Dingkang Yang, Xiaoguang Zhu, and Liang Song. 2022. Learning appearance-
motionnormalityforvideoanomalydetection.In 2022IEEEInternationalConferenceonMultimediaandExpo(ICME) .
IEEE, 1–6.
[102] Yang Liu, Jing Liu, Xiaoguang Zhu, Donglai Wei, Xiaohong Huang, and Liang Song. 2022. Learning task-specific
representation for video anomaly detection with spatial-temporal attention. In ICASSP 2022-2022 IEEE International
ConferenceonAcoustics, Speech andSignal Processing (ICASSP) .IEEE, 2190–2194.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:33
[103] YangLiu,ZhaoyangXia,MengyangZhao,DonglaiWei,YuzhengWang,SiaoLiu,BoboJu,GaoyunFang,JingLiu,and
LiangSong.2023.Learningcausality-inspiredrepresentationconsistencyforvideoanomalydetection.In Proceedings
ofthe31st ACMInternational ConferenceonMultimedia . 203–212.
[104] Yang Liu, Dingkang Yang, Gaoyun Fang, Yuzheng Wang, Donglai Wei, Mengyang Zhao, Kai Cheng, Jing Liu,
and Liang Song. 2023. Stochastic video normality network for abnormal event detection in surveillance videos.
Knowledge-Based Systems (2023),110986.
[105] Zhian Liu, Yongwei Nie, Chengjiang Long, Qing Zhang, and Guiqing Li. 2021. A hybrid video anomaly detection
framework via memory-augmented flow reconstruction and flow-guided frame prediction. In Proceedings of the
IEEE/CVFInternational Conference onComputerVision .13588–13597.
[106] Vina Lomte, Satish Singh, Siddharth Patil, Siddheshwar Patil, and Durgesh Pahurkar. 2019. A survey on real world
anomalydetectioninlivevideosurveillancetechniques. InternationalJournalofResearchinEngineering,Scienceand
Management 2,2(2019),2581–5792.
[107] Cewu Lu, Jianping Shi, and Jiaya Jia. 2013. Abnormal event detection at 150 FPS in MATLAB. In Proceedings of the
IEEEInternational ConferenceonComputer Vision .2720–2727.
[108] Cewu Lu, Jianping Shi, Weiming Wang, and Jiaya Jia. 2019. Fast abnormal event detection. International Journal of
Computer Vision 127,8(2019),993–1011.
[109] Yiwei Lu, Frank Yu, Mahesh Kumar Krishna Reddy, and Yang Wang. 2020. Few-shot scene-adaptive anomaly detec-
tion.InEuropean ConferenceonComputerVision .Springer, 125–141.
[110] WeixinLuo,WenLiu,andShenghuaGao.2017.ArevisitofsparsecodingbasedanomalydetectioninstackedRNN
framework.In Proceedings oftheIEEEInternational ConferenceonComputer Vision .341–349.
[111] WeixinLuo,WenLiu,andShenghuaGao.2021.Normalgraph:Spatialtemporalgraphconvolutionalnetworksbased
prediction networkfor skeleton basedvideo anomalydetection. Neurocomputing 444(2021),332–337.
[112] Weixin Luo, Wen Liu, Dongze Lian, and Shenghua Gao. 2021. Future frame prediction network for video anomaly
detection. IEEETransactions onPatternAnalysisand Machine Intelligence (2021).
[113] Weixin Luo, Wen Liu, Dongze Lian, Jinhui Tang, Lixin Duan, Xi Peng, and Shenghua Gao. 2019. Video anomaly
detection with sparse coding inspired deep neural networks. IEEE Transactions on Pattern Analysis and Machine
Intelligence 43,3(2019),1070–1084.
[114] HuiLv,ZhongqiYue,QianruSun,BinLuo,ZhenCui,andHanwangZhang.2023.Unbiasedmultipleinstancelearning
for weakly supervised video anomaly detection. In Proceedings of the IEEE/CVF Conference on Computer Vision and
Pattern Recognition . 8022–8031.
[115] Hui Lv, Chuanwei Zhou, Zhen Cui, Chunyan Xu, Yong Li, and Jian Yang. 2021. Localizing anomalies from weakly-
labeledvideos. IEEETransactions onImage Processing 30(2021),4505–4515.
[116] KeMa,MichaelDoescher,andChristopher Bodden.2015.Anomalydetectionincrowdedscenesusingdensetrajec-
tories.University of Wisconsin-Madison (2015).
[117] Xiaoxiao Ma, Jia Wu, Shan Xue, Jian Yang, Chuan Zhou, Quan Z. Sheng, Hui Xiong, and Leman Akoglu. 2021. A
comprehensive survey on graph anomaly detection with deep learning. IEEE Transactions on Knowledge and Data
Engineering (2021).
[118] VijayMahadevan,WeixinLi,ViralBhalodia,andNunoVasconcelos.2010.Anomalydetectionincrowdedscenes.In
2010 IEEEComputer Society Conference on ComputerVision and Pattern Recognition . IEEE, 1975–1981.
[119] Snehashis Majhi, Ratnakar Dash, and Pankaj Kumar Sa. 2020. Two-stream CNN architecture for anomalous event
detection in real world scenarios. In International Conference on Computer Vision and Image Processing . Springer,
343–353.
[120] Amir Markovitz, Gilad Sharir, Itamar Friedman, Lihi Zelnik-Manor, and Shai Avidan. 2020. Graph embedded pose
clusteringforanomalydetection.In ProceedingsoftheIEEE/CVFConferenceonComputerVisionandPatternRecogni-
tion.10539–10547.
[121] Jefferson Ryan Medel and Andreas Savakis. 2016. Anomaly detection in video using predictive convolutional long
short-term memorynetworks. arXiv preprintarXiv:1612.00390 (2016).
[122] HarshadkumarS.Modi,Dr.Parikh,andA.Dhaval.2022.Asurveyoncrowdanomalydetection. InternationalJournal
ofComputing and Digital Systems 12,1(2022),1081–1096.
[123] RuwanNawarathna,JungHwanOh,JayanthaMuthukudage,WallapakTavanapong,JohnnyWong,PietC.deGroen,
and Shou Jiang Tang. 2014. Abnormal image detection in endoscopy videos using a filter bank and local binary
patterns. Neurocomputing 144(2014),70–91.
[124] Rashmika Nawaratne, Damminda Alahakoon, Daswin De Silva, and Xinghuo Yu. 2019. Spatiotemporal anomaly
detectionusingdeeplearningforreal-timevideosurveillance. IEEETransactionsonIndustrialInformatics 16,1(2019),
393–402.
[125] RashmiranjanNayak,UmeshChandraPati,andSantosKumarDas.2021.Acomprehensivereviewondeeplearning-
basedmethods for video anomalydetection. Image and VisionComputing 106(2021),104078.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:34 Y .L iue ta l.
[126] Khac-Tuan Nguyen, Dat-Thanh Dinh, Minh N. Do, and Minh-Triet Tran. 2020. Anomaly detection in traffic surveil-
lance videos with GAN-based future frame prediction. In Proceedings of the 2020 International Conference on Multi-
media Retrieval . 457–463.
[127] Trong-NguyenNguyenandJeanMeunier.2019.Anomalydetectioninvideosequencewithappearance-motioncor-
respondence. In Proceedings of theIEEE/CVFInternational ConferenceonComputer Vision .1273–1283.
[128] GuansongPang,ChunhuaShen,LongbingCao,andAntonvandenHengel.2021.Deeplearningforanomalydetec-
tion: A review. ACMComputing Surveys (CSUR) 54,2(2021),1–38.
[129] Guansong Pang, Cheng Yan, Chunhua Shen, Anton van den Hengel, and Xiao Bai. 2020. Self-trained deep ordinal
regression for end-to-end video anomaly detection. In Proceedings of the IEEE/CVF Conference on Computer Vision
and Pattern Recognition . 12173–12182.
[130] Wen-Feng Pang, Qian-Hua He, Yong-jian Hu, and Yan-Xiong Li. 2021. Violence detection in videos based on fusing
visual and audio information. In ICASSP 2021-2021 IEEE International Conference on Acoustics, Speech and Signal
Processing (ICASSP) .IEEE, 2260–2264.
[131] HyunjongPark,JongyounNoh,andBumsubHam.2020.Learningmemory-guidednormalityforanomalydetection.
InProceedings of theIEEE/CVFConferenceonComputer Visionand PatternRecognition . 14372–14381.
[132] Seongheon Park, Hanjae Kim, Minsu Kim, Dahye Kim, and Kwanghoon Sohn. 2023. Normality guided multiple
instancelearningforweaklysupervisedvideoanomalydetection.In ProceedingsoftheIEEE/CVFWinterConference
onApplications of ComputerVision .2665–2674.
[133] Lam Pham, Dat Ngo, Tho Nguyen, Phu Nguyen, Truong Hoang, and Alexander Schindler. 2022. An audio-visual
datasetanddeeplearningframeworksforcrowdedsceneclassification.In Proceedings ofthe19thInternationalCon-
ference onContent-basedMultimedia Indexing .23–28.
[134] YujiangPuandXiaoyuWu.2022.Audio-guidedattentionnetworkforweaklysupervisedviolencedetection.In 2022
2nd International ConferenceonConsumerElectronics andComputer Engineering (ICCECE) .IEEE, 219–223.
[135] Abel Gawel, Hermann Blum, Johannes Pankert, Koen Krämer, Luca Bartolomei, Selen Ercan, Farbod Farshidian,
Margarita Chli, Fabio Gramazio, Roland Siegwart, and others. 2019. A fully-integrated sensing and control system
forhigh-accuracymobileroboticbuildingconstruction.In 2019IEEE/RSJInternationalConferenceonIntelligentRobots
and Systems (IROS) . IEEE, 2300–2307.
[136] Rohit Raja, Prakash Chandra Sharma, Md. Rashid Mahmood, and Dinesh Kumar Saini. 2022. Analysis of anomaly
detection insurveillance video: Recenttrends andfuturevision. Multimedia Tools andApplications (2022),1–17.
[137] BharathkumarRamachandraandMichaelJones.2020.StreetScene:Anewdatasetandevaluationprotocolforvideo
anomalydetection.In ProceedingsoftheIEEE/CVFWinterConferenceonApplicationsofComputerVision .2569–2578.
[138] BharathkumarRamachandra,MichaelJones,andRangaVatsavai.2020.LearningadistancefunctionwithaSiamese
networktolocalizeanomaliesinvideos.In ProceedingsoftheIEEE/CVFWinterConferenceonApplicationsofComputer
Vision.2598–2607.
[139] BharathkumarRamachandra,MichaelJ.Jones,andRangaRajuVatsavai.2020.Asurveyofsingle-scenevideoanom-
alydetection. IEEETransactions onPatternAnalysis andMachine Intelligence 44,5 (2020),2293–2312.
[140] MahdyarRavanbakhsh,MoinNabi,HosseinMousavi,EnverSangineto,andNicuSebe.2018.Plug-and-playCNNfor
crowdmotionanalysis:Anapplicationinabnormaleventdetection.In 2018IEEEWinterConferenceonApplications
of ComputerVision(WACV) .IEEE, 1689–1698.
[141] Mahdyar Ravanbakhsh, Moin Nabi, Enver Sangineto, Lucio Marcenaro, Carlo Regazzoni, and Nicu Sebe. 2017. Ab-
normal event detection in videos using generative adversarial nets. In 2017 IEEE International Conference on Image
Processing (ICIP) .IEEE, 1577–1581.
[142] Mahdyar Ravanbakhsh, Enver Sangineto, Moin Nabi, and Nicu Sebe. 2019. Training adversarial discriminators for
cross-channelabnormaleventdetectionincrowds.In 2019IEEEWinterConferenceonApplicationsofComputerVision
(WACV).IEEE, 1896–1904.
[143] JosephRedmon,SantoshDivvala,RossGirshick,andAliFarhadi.2016.Youonlylookonce:Unified,real-timeobject
detection. In Proceedings oftheIEEEConferenceonComputerVision andPattern Recognition . 779–788.
[144] Khosro Rezaee, Sara Mohammad Rezakhani, Mohammad R. Khosravi, and Mohammad Kazem Moghimi. 2021. A
surveyondeeplearning-basedreal-timecrowdanomalydetectionforsecuredistributedvideosurveillance. Personal
and Ubiquitous Computing (2021),1–17.
[145] MehrsanJavanRoshtkhariandMartinD.Levine.2013.Anon-line,real-timelearningmethodfordetectinganomalies
in videos using spatio-temporalcompositions. Computer VisionandImage Understanding 117,10(2013),1436–1452.
[146] Mohammad Sabokrou, Mahmood Fathy, Mojtaba Hoseini, and Reinhard Klette. 2015. Real-time anomaly detection
andlocalizationincrowdedscenes.In ProceedingsoftheIEEEConferenceonComputerVisionandPatternRecognition
Workshops .56–62.
[147] Mohammad Sabokrou, Mohsen Fayyaz, Mahmood Fathy, and Reinhard Klette. 2017. Deep-cascade: Cascading 3D
deep neural networks for fast anomaly detection and localization in crowded scenes. IEEE Transactions on Image
Processing 26,4(2017),1992–2004.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:35
[148] Venkatesh Saligrama and Zhu Chen. 2012. Video anomaly detection based on local statistical aggregates. In 2012
IEEEConferenceonComputer VisionandPattern Recognition . IEEE, 2112–2119.
[149] Venkatesh Saligrama, Janusz Konrad, and Pierre-Marc Jodoin. 2010. Video anomaly identification. IEEE Signal Pro-
cessing Magazine 27,5(2010),18–33.
[150] Kelathodi Kumaran Santhosh, Debi Prosad Dogra, and Partha Pratim Roy. 2020. Anomaly detection in road traffic
using visualsurveillance: A survey. ACMComputing Surveys (CSUR) 53,6(2020),1–26.
[151] Sam Sattarzadeh, Mahesh Sudhakar, and Konstantinos N. Plataniotis. 2021.SVEA: A small-scalebenchmark for val-
idating the usability of post-hoc explainable AI solutions in image and signal recognition. In Proceedings of the
IEEE/CVFInternational Conference onComputerVision .4158–4167.
[152] Shital Shah, Debadeepta Dey, Chris Lovett, and Ashish Kapoor. 2018. AirSim: High-fidelity visual and physical sim-
ulationfor autonomous vehicles. In Fieldand Service Robotics . Springer,621–635.
[153] YimengShang,XiaoyuWu,andRuiLiu.2022.Multimodalviolentvideorecognitionbasedonmutualdistillation.In
ChineseConference onPatternRecognition andComputer Vision(PRCV) .Springer, 623–637.
[154] MdHaidarSharif,LeiJiao,andChristianW.Omlin.2022.Deepcrowdanomalydetection:State-of-the-art,challenges,
andfuture research directions. arXiv preprintarXiv:2210.13927 (2022).
[155] PrakharSinghandVinodPankajakshan.2018.Adeeplearningbasedtechniqueforanomalydetectioninsurveillance
videos.In 2018Twenty-FourthNationalConferenceonCommunications(NCC) .1–6.https://doi.org/10.1109/NCC.2018.
8599969
[156] Liang Song, Xing Hu, Guanhua Zhang, Petros Spachos, Konstantinos N. Plataniotis, and Hequan Wu. 2022. Net-
workingsystemsofAI:Ontheconvergenceofcomputingandcommunications. IEEEInternetofThingsJournal 9,20
(2022),20352–20381.
[157] RupeshK.Srivastava,KlausGreff,andJürgenSchmidhuber.2015.Trainingverydeepnetworks. AdvancesinNeural
Information Processing Systems 28(2015).
[158] Waqas Sultani, Chen Chen, and Mubarak Shah. 2018. Real-world anomaly detection in surveillance videos. In Pro-
ceedings oftheIEEEConferenceon Computer Vision andPattern Recognition . 6479–6488.
[159] Qianru Sun, Hong Liu, and Tatsuya Harada. 2017. Online growing neural gas for anomaly detection in changing
surveillance scenes. Pattern Recognition 64(2017),187–201.
[160] Shengyang Sun and Xiaojin Gong. 2023. Hierarchical semantic contrast for scene-aware video anomaly detection.
InProceedings oftheIEEE/CVFConferenceonComputer VisionandPattern Recognition . 22846–22856.
[161] ChristianSzegedy,VincentVanhoucke,SergeyIoffe,JonShlens,andZbigniewWojna.2016.Rethinkingtheinception
architecture for computer vision. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition .
2818–2826.
[162] Yao Tang, Lin Zhao, Shanshan Zhang, Chen Gong, Guangyu Li, and Jian Yang. 2020. Integrating prediction and
reconstruction for anomalydetection. PatternRecognition Letters 129(2020),123–130.
[163] ZhengTang,RenshuGu,andJenq-NengHwang.2018.Jointmulti-viewpeopletrackingandposeestimationfor3D
scenereconstruction. In 2018 IEEEInternational ConferenceonMultimedia and Expo(ICME) .IEEE, 1–6.
[164] YuTian,GuansongPang,YuanhongChen,RajvinderSingh,JohanW.Verjans,andGustavoCarneiro.2021.Weakly-
supervisedvideoanomalydetectionwithrobusttemporalfeaturemagnitudelearning.In ProceedingsoftheIEEE/CVF
International ConferenceonComputer Vision .4975–4986.
[165] Du Tran, Lubomir Bourdev, Rob Fergus, Lorenzo Torresani, and Manohar Paluri. 2015. Learning spatiotemporal
features with 3D convolutional networks. In Proceedings of the IEEE International Conference on Computer Vision .
4489–4497.
[166] Hanh T. M. Tran and David Hogg. 2017. Anomaly detection using a convolutional winner-take-all autoencoder. In
Proceedings oftheBritish Machine Vision Conference2017 . British Machine Vision Association.
[167] RaduTudorIonescu,SorinaSmeureanu,BogdanAlexe,andMariusPopescu.2017.Unmaskingtheabnormalevents
invideo. In Proceedings of theIEEEInternational ConferenceonComputerVision .2895–2903.
[168] FrancescoTurchini,LorenzoSeidenari,andAlbertoDelBimbo.2017.Convexpolytopeensemblesforspatio-temporal
anomalydetection.In International ConferenceonImage Analysis andProcessing . Springer,174–184.
[169] AshishVaswani,NoamShazeer,NikiParmar,JakobUszkoreit,LlionJones,AidanN.Gomez,ŁukaszKaiser,andIllia
Polosukhin. 2017.Attentionis allyou need. Advances in Neural Information ProcessingSystems 30(2017).
[170] Hung Vu, Tu Dinh Nguyen, Trung Le, Wei Luo, and Dinh Phung. 2019. Robust anomaly detection in videos using
multilevelrepresentations.In Proceedings of theAAAIConferenceonArtificial Intelligence ,Vol. 33.5216–5223.
[171] Hung Vu, Dinh Phung, Tu Dinh Nguyen, Anthony Trevors, and Svetha Venkatesh. 2017. Energy-based models for
video anomalydetection. arXiv preprint arXiv:1708.05211 (2017).
[172] Boyang Wan, Yuming Fang, Xue Xia, and Jiajie Mei. 2020. Weakly supervised video anomaly detection via center-
guided discriminativelearning.In 2020 IEEEInternational ConferenceonMultimedia and Expo(ICME) .IEEE, 1–6.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:36 Y .L iue ta l.
[173] HengWang,AlexanderKläser,CordeliaSchmid,andCheng-LinLiu.2013.Densetrajectoriesandmotionboundary
descriptors for actionrecognition. International Journal ofComputer Vision 103,1(2013),60–79.
[174] JindongWang,CuilingLan,ChangLiu,YidongOuyang,TaoQin,WangLu,YiqiangChen,WenjunZeng,andPhilip
Yu.2022.Generalizingtounseendomains:Asurveyondomaingeneralization. IEEETransactionsonKnowledgeand
Data Engineering (2022).
[175] Le Wang, Junwen Tian, Sanping Zhou, Haoyue Shi, and Gang Hua. 2023. Memory-augmented appearance-motion
network for video anomalydetection. Pattern Recognition 138(2023),109335.
[176] Limin Wang, Yuanjun Xiong, Zhe Wang, Yu Qiao, Dahua Lin, Xiaoou Tang, and Luc Van Gool. 2016. Temporal
segmentnetworks:Towardsgoodpracticesfordeepactionrecognition.In EuropeanConferenceonComputerVision .
Springer, 20–36.
[177] Tian Wang, Meina Qiao, Zhiwei Lin, Ce Li, Hichem Snoussi, Zhe Liu, and Chang Choi. 2018. Generative neural
networks for anomaly detection in crowded scenes. IEEE Transactions on Information Forensics and Security 14, 5
(2018),1390–1399.
[178] XuanzhaoWang,ZhengpingChe,BoJiang,NingXiao,KeYang,JianTang,JiepingYe,JingyuWang,andQiQi.2021.
Robustunsupervisedvideoanomalydetectionbymultipathframeprediction. IEEETransactionsonNeuralNetworks
and Learning Systems (2021).
[179] Yuzheng Wang, Zhaoyu Chen, Dingkang Yang, Yang Liu, Siao Liu, Wenqiang Zhang, and Lizhe Qi. 2023. Adversar-
ial contrastive distillation with adaptive denoising. In ICASSP 2023-2023 IEEE International Conference on Acoustics,
Speech andSignal Processing (ICASSP) .IEEE, 1–5.
[180] Yuzheng Wang, Zhaoyu Chen, Jie Zhang, Dingkang Yang, Zuhao Ge, Yang Liu, Siao Liu, Yunquan Sun, Wen-
qiang Zhang, and Lizhe Qi. 2023. Sampling to distill: Knowledge transfer from open-world data. arXiv preprint
arXiv:2307.16601 (2023).
[181] Donglai Wei, Yang Liu, Xiaoguang Zhu, Jing Liu, and Xinhua Zeng. 2022. MSAF: Multimodal supervise-attention
enhanced fusion forvideo anomalydetection. IEEESignal Processing Letters 29(2022),2178–2182.
[182] Dong-Lai Wei, Chen-Geng Liu, Yang Liu, Jing Liu, Xiao-Guang Zhu, and Xin-Hua Zeng. 2022. Look, listen and pay
moreattention:Fusingmulti-modalinformationforvideoviolencedetection.In ICASSP2022-2022IEEEInternational
ConferenceonAcoustics, Speech andSignal Processing (ICASSP) .IEEE, 1980–1984.
[183] Garrett Wilson and Diane J. Cook. 2020. A survey of unsupervised deep domain adaptation. ACM Transactions on
IntelligentSystems andTechnology (TIST) 11,5(2020),1–46.
[184] Jhih-Ciang Wu, He-Yen Hsieh, Ding-Jie Chen, Chiou-Shann Fuh, and Tyng-Luh Liu. 2022. Self-supervised Sparse
Representationfor Video AnomalyDetection.In European ConferenceonComputerVision .Springer, 729–745.
[185] PengWu,JingLiu,andFangShen.2019.Adeepone-classneuralnetworkforanomalouseventdetectionincomplex
scenes.IEEETransactions on Neural Networks and Learning Systems 31,7(2019),2609–2622.
[186] PengWu,JingLiu,YujiaShi,YujiaSun,FangtaoShao,ZhaoyangWu,andZhiweiYang.2020.Notonlylook,butalso
listen:Learningmultimodalviolencedetectionunderweaksupervision.In EuropeanConferenceonComputerVision .
Springer, 322–339.
[187] Peng Wu, Xiaotao Liu, and Jing Liu. 2022. Weakly supervised audio-visual violence detection. IEEE Transactions on
Multimedia (2022).
[188] PeihaoWu,WenqianWang,FaliangChang,ChunshengLiu,andBinWang.2023.DSS-Net:Dynamicself-supervised
network for video anomalydetection. IEEETransactions onMultimedia (2023).
[189] Xiaowei Xiang, Yang Liu, Gaoyun Fang, Jing Liu, and Mengyang Zhao. 2023. Two-stage alignments framework for
unsupervised domainadaptationontimeseries data. IEEESignal ProcessingLetters (2023).
[190] DanXu,ElisaRicci,YanYan,JingkuanSong,andNicuSebe.2015.Learningdeeprepresentationsofappearanceand
motion foranomalous eventdetection. arXiv preprint arXiv:1510.01553 (2015).
[191] DanXu,YanYan,ElisaRicci,andNicuSebe.2017.Detectinganomalouseventsinvideosbylearningdeeprepresen-
tations of appearanceandmotion. Computer Vision and Image Understanding 156(2017),117–127.
[192] HangXu,LeweiYao,WeiZhang,XiaodanLiang,andZhenguoLi.2019.Auto-FPN:Automaticnetworkarchitecture
adaptation for object detection beyond classification. In Proceedings of the IEEE/CVF International Conference on
Computer Vision .6649–6658.
[193] DingkangYang,ShuaiHuang,ShunliWang,YangLiu,PengZhai,LiuzhenSu,MingchengLi,andLihuaZhang.2022.
Emotionrecognitionformultiplecontextawareness.In EuropeanConferenceonComputerVision .Springer,144–162.
[194] DingkangYang,ShuaiHuang,ZhiXu,ZhenpengLi,ShunliWang,MingchengLi,YuzhengWang,YangLiu,KunYang,
Zhaoyu Chen, Yan Wang, Jing Liu, Peixuan Zhang, Peng Zhai, and Lihua Zhang. 2023. Aide: A vision-driven multi-
view,multi-modal,multi-taskingdatasetforassistivedrivingperception.In ProceedingsoftheIEEE/CVFInternational
ConferenceonComputer Vision .20459–20470.
[195] Dingkang Yang, Yang Liu, Can Huang, Mingcheng Li, Xiao Zhao, Yuzheng Wang, Kun Yang, Yan Wang, Peng Zhai,
andLihuaZhang.2023.Targetandsourcemodalityco-reinforcementforemotionunderstandingfromasynchronous
multimodalsequences. Knowledge-Based Systems 265(2023),110370.
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
GVAED: Systematic Taxonomy andComparison of Deep Models 189:37
[196] KunYang,DingkangYang,JingyuZhang,MingchengLi,YangLiu,JingLiu,HanqiWang,PengSun,andLiangSong.
2023. Spatio-temporal domain awareness for multi-agent collaborative perception. arXiv preprint arXiv:2307.13929
(2023).
[197] ZhiweiYang,JingLiu,ZhaoyangWu,PengWu,andXiaotaoLiu.2023.Videoeventrestorationbasedonkeyframes
for video anomaly detection. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition .
14592–14601.
[198] Muchao Ye, Xiaojiang Peng, Weihao Gan, Wei Wu, and Yu Qiao. 2019. AnoPCN: Video anomaly detection via deep
predictivecoding network.In Proceedings ofthe27th ACMInternational ConferenceonMultimedia . 1805–1813.
[199] QingzeYin,GuodongDing,ShaogangGong,andZhenminTang.2021.Multi-viewlabelpredictionforunsupervised
learningperson re-identification. IEEESignal Processing Letters 28,(2021),1390–1394.
[200] GuangYu,SiqiWang,ZhipingCai,XinwangLiu,EnZhu,andJianpingYin.2023.Videoanomalydetectionviavisual
Cloze tests. IEEETransactions onInformation Forensicsand Security (2023).
[201] Guang Yu, Siqi Wang, Zhiping Cai, En Zhu, Chuanfu Xu, Jianping Yin, and Marius Kloft. 2020. Cloze test helps:
Effectivevideoanomalydetectionvialearningtocompletevideoevents.In Proceedingsofthe28thACMInternational
Conferenceon Multimedia . 583–591.
[202] Jongmin Yu, Younkwan Lee, Kin Choong Yow, Moongu Jeon, and Witold Pedrycz. 2021. Abnormal event detection
andlocalizationviaadversarialeventprediction. IEEETransactionsonNeuralNetworksandLearningSystems (2021).
[203] Jiashuo Yu,Jinyu Liu,YingCheng,RuiFeng,andYuejie Zhang.2022.Modality-awarecontrastiveinstancelearning
with self-distillation for weakly-supervised audio-visual violence detection. In Proceedings of the 30th ACM Interna-
tional Conferenceon Multimedia . 6278–6287.
[204] Hongchun Yuan, Zhenyu Cai, Hui Zhou, Yue Wang, and Xiangzhi Chen. 2021. TransAnomaly: Video anomaly de-
tectionusing video vision transformer. IEEEAccess 9(2021),123977–123986.
[205] MuhammadZaighamZaheer,Jin-haLee,MarcellaAstrid,andSeung-IkLee.2020.Oldisgold:Redefiningtheadver-
sariallylearnedone-classclassifiertrainingparadigm.In ProceedingsoftheIEEE/CVFConferenceonComputerVision
and Pattern Recognition . 14183–14193.
[206] Muhammad Zaigham Zaheer, Arif Mahmood, Marcella Astrid, and Seung-Ik Lee. 2020. CLAWS: Clustering assisted
weakly supervised learning with normalcy suppression for anomalous event detection. In European Conference on
Computer Vision .Springer,358–376.
[207] M. Zaigham Zaheer, Arif Mahmood, M. Haris Khan, Mattia Segu, Fisher Yu, and Seung-Ik Lee. 2022. Generative
Cooperative Learning for Unsupervised Video Anomaly Detection. In Proceedings of the IEEE/CVF Conference on
Computer Vision andPattern Recognition . 14744–14754.
[208] MuhammadZaighamZaheer,ArifMahmood,HochulShin,andSeung-IkLee.2020.Aself-reasoningframeworkfor
anomalydetectionusing video-levellabels. IEEESignal Processing Letters 27(2020),1705–1709.
[209] Cheng Zhan, Han Hu, Zhi Wang, Rongfei Fan, and Dusit Niyato. 2019. Unmanned aircraft system aided adaptive
video streaming:A jointoptimizationapproach. IEEETransactions onMultimedia 22,3(2019),795–807.
[210] XiaohangZhan,JiahaoXie,ZiweiLiu,Yew-SoonOng,andChenChangeLoy.2020.Onlinedeepclusteringforunsu-
pervisedrepresentationlearning.In ProceedingsoftheIEEE/CVFConferenceonComputerVisionandPatternRecogni-
tion.6688–6697.
[211] Chen Zhang, Guorong Li, Yuankai Qi, Shuhui Wang, Laiyun Qing, Qingming Huang, and Ming-Hsuan Yang. 2023.
Exploiting completeness and uncertainty of pseudo labels for weakly supervised video anomaly detection. In Pro-
ceedings oftheIEEE/CVFConferenceonComputer Visionand PatternRecognition . 16271–16280.
[212] Dasheng Zhang, Chao Huang, Chengliang Liu, and Yong Xu. 2022. Weakly supervised video anomaly detection via
transformer-enabledtemporalrelationlearning. IEEESignal ProcessingLetters (2022).
[213] JiangongZhang,LaiyunQing,andJunMiao.2019.Temporalconvolutionalnetworkwithcomplementaryinnerbag
lossforweaklysupervisedanomalydetection.In 2019IEEEInternationalConferenceonImageProcessing(ICIP) .IEEE,
4030–4034.
[214] Qianqian Zhang, Guorui Feng, and Hanzhou Wu. 2022. Surveillance video anomaly detection via non-local U-Net
frameprediction. Multimedia Tools andApplications (2022),1–16.
[215] YingZhang,Huchuan Lu,LiheZhang,andXiangRuan.2016.Combiningmotionandappearancecuesforanomaly
detection. Pattern Recognition 51(2016),443–452.
[216] Zhenzhen Zhang, Jianjun Hou, Qinglong Ma, and Zhaohong Li. 2015. Efficient video frame insertion and deletion
detectionbasedoninconsistencyofcorrelationsbetweenlocalbinarypatterncodedframes. SecurityandCommuni-
cation Networks 8,2(2015),311–320.
[217] ZheZhang,ShiyaoMa,ZhaohuiYang,ZehuiXiong,JiawenKang,YiWu,KejiaZhang,andDusitNiyato.2022.Robust
semi-supervised federated learning for images automatic recognition in internet of drones. IEEE Internet of Things
Journal(2022).
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
189:38 Y .L iue ta l.
[218] MengyangZhao,YangLiu,JingLiu,andXinhuaZeng.2022.Exploitingspatial-temporalcorrelationsforvideoanom-
alydetection.In 2022 26th International Conference on Pattern Recognition (ICPR) .IEEE, 1727–1733.
[219] Yiru Zhao, Bing Deng, Chen Shen, Yao Liu, Hongtao Lu, and Xian-Sheng Hua. 2017. Spatio-temporal autoencoder
for video anomalydetection.In Proceedings ofthe25th ACMInternational ConferenceonMultimedia . 1933–1941.
[220] Jia-Xing Zhong, Nannan Li, Weijie Kong, Shan Liu, Thomas H. Li, and Ge Li. 2019. Graph convolutional label noise
cleaner: Train a plug-and-play action classifier for anomaly detection. In Proceedings of the IEEE/CVF Conference on
Computer Vision and Pattern Recognition . 1237–1246.
[221] Joey Tianyi Zhou, Jiawei Du, Hongyuan Zhu, Xi Peng, Yong Liu, and Rick Siow Mong Goh. 2019. AnomalyNet: An
anomaly detection network for video surveillance. IEEE Transactions on Information Forensics and Security 14, 10
(2019),2537–2550.
[222] Shifu Zhou, Wei Shen, Dan Zeng, Mei Fang, Yuanwang Wei, and Zhijiang Zhang. 2016. Spatial–temporal convolu-
tional neural networks for anomaly detection and localization in crowded scenes. Signal Processing: Image Commu-
nication47(2016),358–368.
[223] Yi Zhu and Shawn Newsam. 2019. Motion-aware feature for improved video anomaly detection. In The British
Machine Vision Conference .1–12.
Received 10 February 2023; revised 18 November 2023; accepted 31 January 2024
ACMComput.Surv., Vol. 56,No. 7,Article 189.Publicationdate:April 2024.
