METADATA:
------------------------------
Title: 
Author: 
Subject: 
Creator: LaTeX with hyperref
Producer: pdfTeX-1.40.25
CreationDate: D:20241022174824Z
Pages: 22

FULL TEXT:
------------------------------
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 1
Deep Learning for Video Anomaly Detection:
A Review
<PERSON>g <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and
<PERSON><PERSON> Senior Member, IEEE
Abstract —Video anomaly detection (V AD) aims to discover
behaviors or events deviating from the normality in videos. As
a long-standing task in the field of computer vision, V AD has
witnessed much good progress. In the era of deep learning, with
the explosion of architectures of continuously growing capability
and capacity, a great variety of deep learning based methods
are constantly emerging for the V AD task, greatly improving the
generalization ability of detection algorithms and broadening the
application scenarios. Therefore, such a multitude of methods
and a large body of literature make a comprehensive survey a
pressing necessity. In this paper, we present an extensive and
comprehensive research review, covering the spectrum of five
different categories, namely, semi-supervised, weakly supervised,
fully supervised, unsupervised and open-set supervised V AD,
and we also delve into the latest V AD works based on pre-
trained large models, remedying the limitations of past reviews
in terms of only focusing on semi-supervised V AD and small
model based methods. For the V AD task with different levels of
supervision, we construct a well-organized taxonomy, profoundly
discuss the characteristics of different types of methods, and show
their performance comparisons. In addition, this review involves
the public datasets, open-source codes, and evaluation metrics
covering all the aforementioned V AD tasks. Finally, we provide
several important research directions for the V AD community.
Index Terms —Video anomaly detection, anomaly detection,
video understanding, deep learning.
I. I NTRODUCTION
ANOMALY represents something that deviates from what
is standard, normal, or expected. There are myriads of
normalities, and anomalies are considerably scarce. However,
when anomalies do appear, they often have a negative impact.
Anomaly detection aims to discover these rare anomalies built
on top of machine learning, thereby reducing the cost of man-
ual judgment. Anomaly detection has widespread application
across various fields [1], such as financial fraud detection,
network intrusion detection, industrial defect detection, and
human violence detection. Among these, video anomaly de-
tection (V AD) occupies an important place, in which anomaly
indicates the abnormal events in the temporal or spatial dimen-
sions. V AD not only plays a vital role in intelligent security
(e.g., violence, intrusion, and loitering detection) but is also
widely used in other scenarios, such as online video content
Peng Wu, Chengyu Pan, Yuting Yan, Peng Wang, and Yanning Zhang are
with the School of Computer Science, Northwestern Polytechnical University,
China. E-mail: <EMAIL>; {peng.wang, ynzhang }@nwpu.edu.cn.
Guansong Pang is with the School of Computing and Information Systems,
Singapore Management University Singapore, Singapore. E-mail: pangguan-
<EMAIL>.
Manuscript received April 19, 2021; revised August 16, 2021. (Correspond-
ing author: Guansong Pang. Chengyu Pan and Yuting Yan contributed equally.)
050100150200250300350400
2016 2017 2018 2019 2020 2021 2022 2023
020406080100120140160
2016 2017 2018 2019 2020 2021 2022 2023Publications
PublicationsFig. 1. Publications on V AD. Left: Google Scholar; Right: IEEE Xplore.
6068768492
2016 2017 2018 2019 2020 2021 2022 2023AUC (%)Avenue ShanghaiTech
7478828690
2018 2019 2020 2021 2022 2023 2024AUC\AP (%)UCF-Crime XD-Violence
Fig. 2. Performance development for semi/weakly supervised V AD tasks.
review and traffic anomaly prediction in autonomous driving
[2]. Owing to its significant potential for applications across
different fields, V AD has attracted considerable attention from
both industry and academia.
In the pre-deep learning era, the routine way is to separate
feature extraction and classifier design, which forms a two-
stage process, and then combine them together during the
inference stage. First, there is a feature extraction process
to convert the original high dimensional raw videos into
compact hand-crafted features based on prior knowledge of
experts. Although hand-crafted features lack robustness and
are difficult to use for capturing effective behavior expressions
in the face of complex scenarios, these pioneer works deeply
enlighten subsequent deep learning based works.
The rise of deep learning has made traditional machine
learning algorithms fall out of favor over the last decade.
With the rapid development of computer hardware and the
massive data in the Internet era, we have witnessed great
progress in developing deep learning based methods for V AD
in recent years. For example, ConvAE [3], the first work using
deep auto-encoders based on convolutional neural networks
(CNNs) for capturing the regularities in videos; FuturePred
[4], the first work making use of U-Net for forecasting future
anomalies; DeepMIL [5], the first endeavor exploring deep
multiple instance learning (MIL) framework for real-world
anomalies. In order to more intuitively manifest the research
enthusiasm for the V AD task in the era of deep learning, we
conduct a statistical survey on the number of publications
related to V AD over the past decade (the era driven by
the rise of deep learning based methods) through GooglearXiv:2409.05383v1  [cs.CV]  9 Sep 2024
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 2
TABLE I
ANALYSIS AND COMPARISON OF RELATED REVIEWS .
Reference Year Main Focus Main CategorizationResearch Topics
UV AD WV AD SV AD FV AD OV AD LV AD IV AD
Ramachandra et al. [8] 2020 Semi-supervised single-scene V AD Methodology % % ! % % % %
Santhosh et al. [9] 2020 V AD applied on road traffic Methodology ! % ! ! % % %
Nayak et al. [10] 2021 Deep learning driven semi-supervised V AD Methodology % % ! % % % %
Tran et al. [11] 2022 Semi&weakly supervised V AD Architecture % % ! % % % %
Chandrakala et al. [12] 2023 Deep model-based one&two-class V AD Methodology&Architecture % ! ! ! % % %
Liu et al. [13] 2023 Deep models for semi&weakly supervised V AD Model Input ! ! ! ! % % %
Our survey 2024 Comprehensive V AD taxonomy and deep modelsMethodology, Architecture, Refinement! ! ! ! ! ! !Model Input, Model Output
UV AD: Unsupervised V AD, WV AD: Weakly supervised V AD, SV AD: Semi-supervised V AD, FV AD: Fully supervised V AD, OV AD: Open-set supervised
V AD, LV AD: Large-model based V AD, IV AD: Interpretable V AD
Scholar and IEEE Xplore1. We select five related topics, i.e.,
video anomaly detection, abnormal event detection, abnormal
behavior detection, anomalous event detection, and anomalous
behavior detection, and showcase the publication statistics in
Figure 1. It is not hard to see that the number of related
publications counted from both sources exhibits a steady and
rapid growth trend, demonstrating that V AD has garnered
widespread attention. Moreover, we also demonstrate the de-
tection performance trends of annual state-of-the-art methods
on commonly used datasets under two common supervised
manners, and present performance trends in Figure 2. The
detection performance shows a steady upward trend across
all datasets, without displaying any performance bottleneck.
For instance, the performance of semi-supervised methods on
CUHK Avenue [6] has experienced a significant surge, rising
from 70.2% AUC [3] to an impressive 90.1% AUC [7] over
the past seven-year period. Moreover, for the subsequently
proposed weakly supervised V AD, significant progress has
been achieved as well. This indicates the evolving capability
of deep learning methods under developing architectures, and
also showcases the ongoing exploration enthusiasm of deep
learning methods for the V AD task.
The above statistics clearly demonstrate that deep learning
driven V AD is the hot area of the current research. There-
fore, there is an urgent necessity for a systematic taxonomy
and comprehensive summary of existing works, to facilitate
newcomers as a guide and provide references for existing
researchers. Based on this, we first collect some high-profile
reviews on V AD in the past few years, which are shown
in Table I. Ramachandra et al. [8] mainly focused on semi-
supervised V AD in the single scenario, lacking in discussions
of cross scenes. Santhosh et al. [9] reviewed V AD methods
focusing on entities in road traffic scenarios. Their reviews lack
sufficient in-depth analysis and center on pre-2020 method-
ologies, resulting in the neglect of recent advances. Nayak
et al. [10] comprehensively surveyed on deep learning based
methods for semi-supervised V AD, but did not take into
account weakly supervised V AD methods. The follow-up work
Tran et al. [11] introduced a review of the emerging weakly
supervised V AD, but the focus is not only on videos but also
on image anomaly detection, resulting in a lack of systematic
organization of the V AD task. More recently, both Chandrakala
et al. [12] and Liu et al. [13] constructed an organized
taxonomy covering a variety of V AD tasks, e.g., unsupervised
1https://scholar.google.com; https://ieeexplore.ieee.org/V AD, semi-supervised V AD, weakly supervised V AD, and
fully supervised V AD, and also surveyed deep learning based
methods for most supervised V AD tasks. However, they restrict
their scope to the conventional close-set scenario, and fail to
cover the latest research in the field of open-set supervised
V AD, without introducing a brand-new pipeline based on pre-
trained large models and interpretable learning.
To address this gap comprehensively, we present a thorough
survey of V AD works in the deep learning era. Our survey
covers several key aspects to provide a comprehensive analysis
of V AD studies. To be specific, we perform an in-depth
investigation into the development trends of V AD task in the
era of deep learning, and then propose a unified framework
that integrates different V AD tasks together, filling the gaps
in the existing reviews in terms of taxonomy. We then collect
the most comprehensive open sources, including benchmark
datasets, evaluation metrics, open-source codes, and perfor-
mance comparisons, to help researchers in this field avoid
detours and improve efficiency. Further, we systematically
categorize various V AD tasks, dividing existing works into
different categories and establishing a clear and structured tax-
onomy system to provide a coherent and organized overview
of various V AD paradigms. In addition to this taxonomy, we
conduct a comprehensive analysis of each paradigm. Further-
more, throughout this survey, we spotlight influential works
that have significantly contributed to the research advancement
in V AD.
The main contributions of this survey are summarized in
the following three aspects:
•We provide a comprehensive review of V AD, covering
five tasks based on different supervision signals, i.e.,
semi-supervised V AD, weakly supervised V AD, fully
supervised V AD, unsupervised V AD, and open-set su-
pervised V AD. The research focus has expanded from
traditional single-task semi-supervised V AD to a broader
range of multiple V AD tasks.
•Staying abreast of the research trends, we review the
latest studies on open-set supervised V AD. Moreover, we
also revisit the most recent V AD methods based on pre-
trained large models and interpretable learning. The emer-
gence of these methods elevates both the performance and
application prospects of V AD. To our knowledge, this
is the first comprehensive survey of open-set supervised
V AD and pre-trained large model based V AD methods.
•For different tasks, we systematically review existing
deep learning based methods, and more importantly, we
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 3
U
 U
 U…N Normal Video A Anomalous Video U Unlabeled Video
(a) Fully supervised VAD(b) Semi-supervised VAD(c) Weakly supervised VAD(d) Unsupervised VAD
N
 A
 N…AUnseen Anomalous Video
N
 A
 N…
N
 A
 N…FFrame-level LabelV Video-level Label
N
 N
 N…F V V FOR
N
 A
 N…
N
 A
 N…
N
 A
 N…
U
 U
 U…
N A
 N…Train
Test
(e) Open-set supervised VAD
Fig. 3. Comparisons of five supervised V AD tasks, i.e., fully supervised, semi-supervised, weakly supervised, unsupervised, and open-set supervised V AD.
introduce a unified taxonomy framework categorizing the
methods from various V AD paradigms based on various
aspects, including model input, architecture, methodol-
ogy, model refinement, and output. This meticulous sci-
entific taxonomy enables a comprehensive understanding
of the field.
II. B ACKGROUND
A. Notation and Taxonomy
As aforementioned, the studied problem, V AD, can be for-
mally divided into five categories based on supervision signals.
Different supervised V AD tasks aim to identify anomalous
behaviors or events, but with different training and testing
setups. We demonstrate these different V AD tasks in Figure 3.
The general V AD problem is presented as follows. Suppose
we are given a set of training samples X={xi}N+A
i=1 and
corresponding labels Y, where Xn={xi}N
i=1is the set of
normal samples and Xa={xi}N+A
i=N+1is the set of abnormal
samples. Each sample xiis accompanied by a corresponding
supervision label yiinY. During the training phase, the
detection model Φ(θ)takesXas input and generates anomaly
predictions; it is then optimized according to the following
objective,
l=L(Φ (θ,X),XorY) (1)
where L(·)is employed to quantify the discrepancy between
the predictions and the ground-truth labels or original samples.
During inference, the detection model is expected to locate the
abnormal behaviors or events in videos based on the generated
anomaly predictions. Depending on the input to L, V AD can
be categorized into one of the following five task settings.
Semi-supervised V AD assumes that only normal samples
are available during the training stage, that is, Xa=∅. This
task aims to learn the normal patterns based on the training
samples and consider the test samples which fall outside the
learned patterns as anomalies. Pros and Cons: Only normal
samples are required for training, hence, there is no need to
painstakingly collect scarce abnormal samples. However, any
unseen test sample may be recognized as abnormal, leading
to a higher false positive rate.
Weakly supervised V AD has more sufficient training sam-
ples and supervision signals than semi-supervised V AD. Both
normal and abnormal samples are provided during the training
stage, but the precise location stamps of anomalies in these
untrimmed videos are unknown. In other words, only coarsevideo-level labels are available (i.e., inexact supervision).
Formally, Y={0,1}N+A
i=1, where yi= 0 indicates that xi
is normal, and yi= 1 indicates that xiis abnormal. Pros
and Cons: Compared to fully supervised annotations, it can
significantly reduce labeling costs. However, it places higher
demands on algorithm design and may lead to situations of
blind guessing.
Fully supervised V AD , as its name implies, comprises
the complete supervision signals, meaning that each abnormal
sample has precise annotations of anomalies. This task can be
viewed as a standard video or frame classification problem.
Due to the scarcity of abnormal behaviors and intensive
manual labeling in reality, there has been little research on the
fully supervised V AD task. It is noteworthy that video violence
detection can be regarded as a fully supervised V AD, hence,
we denote violence detection as a fully supervised V AD task
in this paper. Formally, each video xiinXais accompanied
by a corresponding supervision label yi={(ts
j, te
j)}Ui
j, where
ts
jandte
jdenote the start and end time of the j-th violence
event, Uiindicates the total number of anomalies present in
the video. Pros and Cons: In contrast to weakly supervised
V AD, with full supervised information, the detection perfor-
mance of the algorithms would be remarkable. However, the
corresponding drawback is the high requirement for intensive
manual annotations.
Unsupervised V AD aims to discover anomalies directly
from fully unlabeled videos in an unsupervised manner. Thus,
unsupervised V AD no longer requires labeling normal and
abnormal videos to build the training set. It can be expressed
formally as follows, X=Xtest, and Y=∅, in which
Xtest denotes the set of test samples. Pros and Cons: No
time-consuming effort is needed to collect training samples,
avoiding the heavy labeling burden. Besides, this assumption
also expands the application fields of V AD, implying that
the detection system can continuously retrain without human
intervention. Unfortunately, due to the lack of labels, the
detection performance is relatively poor, leading to a higher
rate of false positives and false negatives.
Open-set supervised V AD is devised to discover unseen
anomalies that are not presented in the training set. Un-
like semi-supervised V AD, open-set supervised V AD includes
abnormal samples in the training set, which are referred
to as seen anomalies. Specifically, for each xiinXa, its
corresponding label yi∈Cbase, here Cbase represents the
set of base (seen) anomaly categories, and Cbase⊂ C, with
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 4
Interpretable 
Learning2D CNN
3D CNN
DiffusionLSTM
GCN
TransformerGANAuto Encoder
Frame Level
Pixel LevelFrame Level
Patch Level
Object LevelSkeleton
Optical Flow
RGB
HybridSelf-supervised 
LearningReconstruction
Prediction
Cloze Test
Jigsaw Puzzles
Contrastive 
Learning
Sparse Coding
Multiple TasksDenoising
Patch InpaintingOne-class 
LearningOne-class 
Classifier
Gaussian 
Classifier
Adversarial 
Classifier
RNNPseudo 
Anomalies
Memory 
BankModel Input Network Architecture Methodology Refinement Model Output
Fig. 4. The taxonomy of semi-supervised V AD. We provide a hierarchical taxonomy that organizes existing deep semi-supervised V AD models by model
input, methodology, network architecture, refinement strategy, and model output into a systematic framework.
C=Cbase∪Cnovel . Here, Cnovel andCrepresent the sets
of novel anomaly categories unseen during training and all
anomaly categories, respectively. Given a testing sample xtest,
its label ytestmay be either ∈Cbase or∈Cnovel .Pros and
Cons: Compared to the two most common tasks, i.e., semi-
supervised V AD and weakly supervised V AD, open-set super-
vised V AD not only reduces false positives but also avoids
being limited to closed-set scenarios, thus demonstrating high
practical value. However, it relies on learning specialized
classifiers, loss functions, or generating unknown classes to
to detect unseen anomalies.
B. Datasets and Metrics
Related benchmark datasets and evaluation metrics are listed
at https://roc-ng.github.io/DeepV AD/.
III. S EMI-SUPERVISED VIDEO ANOMALY DETECTION
Based on our in-depth investigation of past surveys, we
found that previous surveys mostly lack a scientific taxonomy,
in which many surveys simply categorize semi-supervised
V AD works into different groups based on usage approaches,
such as reconstruction-based, distance-based, and probability-
based approaches, and some surveys classify works according
to inputs, such as image-based, optical flow-based, and patch-
based approaches. It is particularly apparent that existing clas-
sification reviews are relatively simplistic and superficial, thus
making it challenging to cover all methods comprehensively
and effectively. To address this issue, we establish a compre-
hensive taxonomy, encompassing model input, methodology,
architecture, model refinement, and model output. The detailed
illustration is presented in Figure 4.
As aforementioned, only normal samples are available for
training in the semi-supervised V AD task, rendering the su-
pervised classification paradigm inapplicable. Common ap-
proaches involve leveraging the intrinsic information of the
training samples to learn deep neural networks (DNNs) for
solving a pretext task. For instance, normality reconstruction
is a classic pretext task [3]. During this process, several critical
aspects need consideration: selection of sample information
(Model Input), design of pretext tasks (Methodology), utiliza-
tion of deep networks (Network Architecture), improvementof methods (Refinement), and expression of anomaly results
(Model Output). These key elements collectively contribute
to the effectiveness of semi-supervised V AD solutions. In the
following sections, we introduce existing deep learning based
V AD methods systematically according to the aforementioned
taxonomy.
A. Model Input
Existing semi-supervised V AD methods typically use the
raw video or its intuitive representations as the model input.
Depending on the modality, these can be categorized as fol-
lows: RGB images, optical flow, skeleton, and hybrid inputs,
where the first three represent appearance, motion, and body
posture, respectively.
1) RGB: RGB images are the most common input for
conventional vision tasks driven by deep learning techniques,
and this holds true for the V AD task as well. Unlike other
modalities, RGB images do not require additional processing
steps such as optical flow calculations or pose estimation
algorithms. In deep learning era, various deep models can be
employed to extract compact and high-level visual features
from these high-dimensional raw data. Utilizing these high-
level features enables the design of more effective subsequent
detection methods. Moreover, depending on the input size,
RGB image based input can be categorized into three principal
groups: frame level, patch level, and object level.
Frame-level RGB input provides a macroscopic view of
the entire scene, encompassing both the background, which
is usually unrelated to the event, and the foreground objects,
where anomalies are more likely to occur. The conventional
approach typically uses multiple consecutive video frames as
a single input to capture temporal context within the video,
as seen in methods like ConvAE [3], ConvLSTM-AE [14],
and STAE [15]. On the other hand, several research studies
focus on using single-frame RGB as input, aiming to detect
anomalies at the spatial level, such as AnomalyGAN [16] and
AMC [17].
Patch-level RGB input involves segmenting the frame-
level RGB input spatially or spatio-temporally, which focuses
on local regions, effectively separating the foreground from
the background and differentiating between various individ-
ual entities. The primary advantage of patch-level input is
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 5
its ability to significantly reduce the interference from the
dominant background, which is usually less relevant to the
anomalies. This segmentation helps in isolating areas that
are more likely to contain anomalies, thereby enhancing the
detection accuracy. For example, AMDN [18], [19], DeepOC
[20] and Deep-cascade [21] took spatio-temporal patches as
the input, and S2-V AE [22] and GM-V AE [23] only employed
an image patch from a single video frame as the model input.
Object-level RGB input has emerged in recent years along-
side the advancement of object detection approaches, which
focuses exclusively on foreground objects. Compared to patch-
level input, it entirely disregards background information and
neglects to consider the relationship between objects and
backgrounds. As a result, it demonstrates impressive perfor-
mance in identifying anomalous events within complex scenes.
Hinami et al. [24] first proposed an object-centric approach
FRCN based on object inputs, a follow-up work ObjectAE [25]
introduced to train object-centric auto-encoders on detected
objects, and subsequently, a series of works focusing on
object-level inputs emerged, e.g., HF2-V AD [26], HSNBM
[27], BDPN [28], ER-V AD [29], and HSC [30].
2) Optical Flow: A video is not merely a sequence of
stacked RGB images, it encapsulates the temporal dimension
and crucial temporal context. Therefore, extracting temporal
context is vital for understanding video content, with mo-
tion information playing an irreplaceable role. Optical flow
represents the motion information between consecutive video
frames and is commonly used as a model input for V AD
task. It typically does not appear alone but is paired with the
corresponding RGB image as input in the two-stream network.
Therefore, it also encompasses frame [4], [31]–[35], patch
[20], [36], [37], and object [26], [29], [35], [38] levels.
3) Skeleton: In recent years, with the remarkable success
of deep learning technologies in the field of pose estima-
tion, V AD methods based on skeleton input have emerged.
Skeleton input solely focuses on the human body, making it
more specialized than object-level RGB input. It demonstrates
impressive performance in human-centric V AD, marking it
as a significant research interest in recent years within the
V AD domain. Morais et al. [39] first endeavored to learn the
normal patterns of human movements using dynamic skeleton,
where the pose estimation is utilized to independently detect
skeletons in each video frame. Then, GEPC [40], MTTP [41],
NormalGraph [42], HSTGCNN [43], TSIF [44], STGCAE-
LSTM [45], STGformer [46], STG-NF [47], MoPRL [48],
MoCoDAD [49], and TrajREC [50] are specialized in human-
related V AD with the skeleton input.
4) Hybrid: The hybrid input from different modalities often
proves more advantageous for the V AD task compared to the
unimodal input due to their complementary nature. In existing
deep learning driven V AD methods, hybrid input is a com-
mon practice. Typical hybrid input includes frame-level RGB
combined with optical flow [4], patch-level RGB combined
with optical flow [20], and object-level RGB combined with
optical flow [26]. More recently, several research studies have
explored hybrid input based on RGB combined with skeleton
[51].B. Methodology
For semi-supervised V AD, only normal samples are pro-
vided during the training phase, rendering conventional su-
pervised classification methods inapplicable. The current ap-
proach involves designing a pretext task based on the prop-
erties inherent in normal samples themselves to encapsulate
a paradigm that encompasses all normal events, referred to
as the normal paradigm or normal pattern. Through exten-
sive research on existing works, we categorize three major
approaches for learning the normal paradigm: self-supervised
learning, one-class learning, and interpretable learning.
1) Self-supervised Learning:
“If intelligence is a cake, the bulk of the cake is
self-supervised learning. ” —— Yann LeCun
Self-supervised learning primarily leverages auxiliary tasks
(pretext tasks) to derive supervisory signals directly from un-
supervised data. Essentially, self-supervised learning operates
without external labeled data, as these labels are generated
from the input data itself. For semi-supervised V AD task,
which lacks explicit supervisory signals, self-supervised learn-
ing naturally becomes essential for learning normal represen-
tations and constructing the normal paradigm based on these
auxiliary tasks. Consequently, self-supervised learning based
methods consistently dominate the leading position in semi-
supervised V AD task. Throughout this process, a significant
research focus and challenge lie in designing effective pretext
tasks derived from the data itself. Here, we compile the
common design principles of auxiliary tasks used in existing
self-supervised learning based methods.
Reconstruction is currently the most commonly used pre-
text task for self-supervised learning based methods in the
field of semi-supervised V AD [3], [14], [17], [22], [48], [52]–
[54]. The main process involves inputting normal data into
the network, performing encoding-decoding operations, and
generating reconstructed data, encouraging the network to
produce reconstructions that closely match the original input
data. The objective can be expressed as,
lrec=L(Φ (θ, x), x) (2)
For convenience, in the following sections, unless otherwise
specified, xrepresent normal data, which could be a normal
video, a normal video frame, a normal feature, or similar.
The above objective function measures the reconstruction
error, which serves as a criterion for determining whether the
test data is anomalous during the test stage. The larger the
reconstruction error, the higher the probability that the data
is considered anomalous. However, due to the high capacity
of deep neural networks, reconstruction based methods cannot
guarantee that larger reconstruction errors for abnormal events
do necessarily happen.
Prediction fully leverages the temporal coherence inherent
in videos, which is also a commonly used pretext task.
This pretext task is based on the assumption that normal
events are predictable, while abnormal ones are unpredictable.
Specifically, the prediction pretext task takes historical data as
input and, through encoding-decoding operations within the
network, outputs the predicted data for the current moment.
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 6
The network is compelled to make the predicted data similar
to the actual current data. We define the optimization objective
for prediction as,
lpre=L(Φ (θ,{It−∆t, ..., I t−1}), It) (3)
Itis the actual data at the current time step t,It−∆t:t−1
represents the historical data from time step t−∆ttot−1.
FuturePred [4], as a future frame prediction framework, pro-
vided a new solution for V AD. Then, many researchers [26],
[33], [46], [55]–[61] proposed other prediction based methods.
It alleviates, to some extent, the problem in reconstruction
based methods where abnormal events can also be well
reconstructed.
Visual cloze test is inspired by the cloze test in natural
language processing [62]–[64]. It mainly involves training
multiple DNNs to infer deliberately erased data from in-
complete video sequences, where the prediction task can be
considered a special case of visual cloze test task, i.e., the
erased data happens to be the last frame in the video sequence.
We define the objective function for completing erased data at
thet-th time stamp as,
l(t)
vct=L(Φ (θ,{I1, ..., I t−1, It+1, ...}), It) (4)
Similar to the prediction task, it also leverages the temporal
relationships in the video, but the difference lies in this task
can learn better high-level semantics and temporal context.
Jigsaw puzzles have recently been applied as a pretext task
in semi-supervised V AD [65]–[67]. The main process involves
creating jigsaw puzzles by performing temporal, spatial, or
spatio-temporal shuffling, and then designing networks to
predict the relative or absolute permutation in time, space, or
both. The optimization function is as follows,
ljig=X
iL 
ti,ˆti
(5)
where tiandˆtidenote the ground-truth and predicted positions
of the i−th data in the original sequence, respectively. Unlike
the previous pretext tasks, which involve high-quality image
generation, jigsaw puzzles are cast as the multi-label classifi-
cation, enhancing computational efficiency and learning more
contextual details.
Contrastive learning is a key approach in self-supervised
learning, where the goal is to learn useful representations by
distinguishing between similar and dissimilar pairs. For semi-
supervised V AD, two samples are regarded as a positive pair
if they originate from the same sample, and otherwise as a
negative sample pair [68]. We show the contrastive loss as
below,
lcon=X
i−logexp(sim( xi, x+
i)/τ)P
kexp(sim( xi, x−
k)/τ)(6)
xiandx+
iare the positive pair, and xiandx−
kare the negative
pair. sim (·,·)is the similarity function (e.g., cosine similarity).
Wang et al. [69] introduced a cluster attention contrast frame-
work for V AD, which is built on top of contrastive learning.
During the inference stage, the highest similarity between the
test sample and its variants is regarded as the regularity score.Lu et al. [70] further proposed a learnable locality-sensitive
hashing with contrastive learning strategy for V AD.
Denoising [71], [72] is very similar to reconstruction, with
the main difference being that noise ηis added to the input
data, and the network is encouraged to achieve a denoising
effect for the reconstructed data. The benefit is that it can
enhance the robustness of network for V AD. The optimization
objective is expressed as,
lden=L(Φ (θ, x+η), x) (7)
Deep sparse coding is encouraged by the success of
traditional sparse reconstruction based V AD methods [73],
upgrade versions leverage deep neural networks for semi-
supervised V AD. Unlike the aforementioned reconstruction or
prediction tasks, sparse coding typically uses extracted high-
level representations rather than raw video image data as input.
By learning from a large amount of normal representations,
a dictionary of normal patterns is constructed. The total
objective is listed as,
lspa=∥x−Bz∥2
2+∥z∥1 (8)
Different normal events can be reconstructed through the dic-
tionary Bmultiplied by the sparse coefficient z. For anomalies,
it is hard to reconstruct them using the linear combination of
elements from the normal dictionary with a sparse coefficient.
To overcome the time-consuming inference and low-level
hand-crafted features of traditional sparse reconstruction based
methods, deep sparse coding based methods are emerged [32],
[74]–[76], simultaneously leveraging the powerful representa-
tion capabilities of DNNs and sparse representation techniques
to improve detection performance and efficiency.
Patch inpainting involves the process of reconstructing
missing or corrupted regions by inferring the missing parts
from the available data. This technique mainly leverages the
spatial and temporal context to predict the content of the
missing regions, ensuring that the reconstructed regions blend
seamlessly with the surrounding regions. The optimization
objective for patch inpainting can be defined to minimize the
difference between the original and the reconstructed patches,
lpat=L 
Φ (θ, x⊙M), x⊙¯M
(9)
Mdenotes a mask, where the value of 0 in the mask indicates
that the position needs to be inpainted, while a value of 1
indicates that the position does not need to be inpainted,
and ¯Mis a reversed M. Different from prediction and vi-
sual cloze test, patch inpainting takes into greater account
the spatial or spatio-temporal context. Zavrtanik et al. [77]
regarded anomaly detection as a reconstruction-by-inpainting
task, further randomly removed partial image regions and
reconstructed the image from partial inpaintings. Then, Ristea
et al. [78], [79] presented a novel self-supervised predictive
architectural building block, a plug-and-play design that can
easily be incorporated into various anomaly detection methods.
More recently, a self-distilled masked auto-encoder [80] was
proposed to inpaint original frames.
Multiple task can ease the dilemma of the single pretext
task, i.e., a single task may be not well aligned with the V AD
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 7
task, thus leading to sub-optimal performance. Recently, sev-
eral works attempted to train V AD models jointly on multiple
pretext tasks. For example, various studies exploited different
self-supervised task compositions, involving reconstruction
and prediction [15], [27], [39], [81], prediction and denoising
[82], [83], prediction and jigsaw puzzles [84], prediction and
constrastive learning [70]. A few works [66], [67], [85],
[86] strove to develop more sophisticated multiple tasks from
different perspectives.
2) One-class Learning: One-class learning primarily fo-
cuses on samples from the normal class. Compared to self-
supervised learning methods, it does not require the strenuous
effort of designing feasible pretext tasks. It is generally divided
into three categories: one-class classifier, Gaussian classifier,
and adversarial classifier from discriminators of generative
adversarial networks (GAN).
One-class classifier basically includes one-class support
vector machine (OC-SVM) [87], support vector data descrip-
tion (SVDD) [88], and other extensions, e.g., basic/generalized
one-class discriminative subspace classifier (BODS, GODS)
[89]. Specifically, OC-SVM is modeled as an extension of
the SVM objective by learning a max-margin hyperplane that
separates the normal from the abnormal in a dataset, which is
learned by minimizing the following objective,
min
w,b,ξ≥01
2∥w∥2
2−b+CX
ξi,s.t. wTxi≥b−ξi,∀xi∈ Xn
(10)
where ξiis the non-negative slack, wandbdenote the
hyperplane, and Cis the slack penalty. AMDN [18] is a typical
OC-SVM based V AD method, which obtains low-dimensional
representations through the auto-encoder, and then uses OC-
SVM to classify all normal representations. Another popular
variant of one-class classifiers is (Deep) SVDD [90], [91] that
instead of modeling data to belong to an open half-space (as
in OC-SVM), assumes the normal samples inhabit a bounded
set, and the optimization seeks the centroid cof a hypersphere
of minimum radius R > 0that contains all normal samples.
Mathematically, the objective reads,
min
c,R,ξ≥01
2R2+CX
ξi,s.t.∥xi−c∥2
2≤R2−ξi,∀xi∈ Xn
(11)
where, as in OC-SVM, the ξimodels the slack. Based on
this, Wu et al. [20] proposed a end-to-end deep one-class
classifier, i.e., DeepOC, for V AD, avoiding the shortcomings
of complicated two-stage training of AMDN.
Gaussian classifier based methods [21], [23], [92] assume
that, in practical applications, the data typically follows a
Gaussian distribution. By using training samples, it learns
the Gaussian distribution (mean µand variance Σ) of the
normal pattern. During the testing phase, samples that deviate
significantly from the mean are considered anomalies. The
abnormal score is presented as,
p(xi) =1
(2π)k
2|Σ|1
2exp
−1
2(xi−µ)TΣ−1(xi−µ)
(12)
Adversarial classifier uses adversarial training between the
generator Gand discriminator Dto learn the distribution
of normal samples. Gis aware of normal data distribution,as the normal samples are accessible to it. Therefore, D
explicitly decides whether the output of Gfollows the normal
distribution or not. Therefore, adversarial classifier can be
jointly learned by optimizing the following objective,
min
Gmax
D
Exi∼pt[log(D(xi))]
+E˜xi∼pt+Nσ[log(1 −D(G( ˜xi)))]
,(13)
where xiis drawn from a normal data distribution ptand˜xi
is the sample xiwith added noise, which is sampled from
a normal distribution Nσ. The final abnormal score of an
input sample xis given as D(G(x)). For instance, Sabokrou
et al. [93]–[95] developed a conventional adversarial network
that contains two sub-networks, where the discriminator works
as the one-class classifier, while the refiner supports it by
enhancing the normal samples and distorting the anomalies. To
mitigate the instability caused by adversarial training, Zaheer
et al. [96], [97] proposed stabilizing adversarial classifiers
by transforming the role of discriminator to distinguish good
and bad quality reconstructions as well as introducing pseudo
anomaly examples.
3) Interpretable Learning: While self-supervised learning
and one-class learning based methods perform competitively
on popular V AD benchmarks, they are entirely dependent
on complex neural networks and mostly trained end-to-end.
This limits their interpretability and generalization capacity.
Therefore, explainable V AD emerges as a solution, which
refers to techniques and methodologies used to identify and
explain unusual events in videos. These techniques are de-
signed not only to detect anomalies but also to provide clear
explanations for why these anomalies are flagged, which is
crucial for trust and transparency in real-world applications.
For example, Hinami et al. [24] leveraged multi-task detector
as the generic model to learn generic knowledge about visual
concepts, e.g., entity, action, and attribute, to describe the
events in the human-understandable form, then designed an
environment-specific model as the anomaly detector for abnor-
mal event recounting and detection. Similarly, Reiss et al. [38]
extracted explicit attribute-based representations, i.e., velocity
and pose, along with implicit semantic representations to make
interpretable anomaly decisions. Coincidentally, Doshi and
Yilmaz [98] proposed a novel framework which monitors both
individuals and the interactions between them, then explores
the scene graphs to provide an interpretation for the context of
anomalies. Singh et al. [99] started a new line for explainable
V AD, a more generic model based on high-level appearance
and motion features which can provide human-understandable
reasons. Compared to previous methods, this work is indepen-
dent of detectors and is capable of locating spatial anomalies.
More recently, Yang et al. [100] proposed the first rule-
based reasoning framework for semi-supervised V AD with
large language models (LLMs) due to LLMs’ revolutionary
reasoning ability. Here, we present some classical explainable
V AD methods in 5.
C. Network Architecture
1) Auto-encoder: It consists of two vital structures, namely,
encoder and decoder, in which the encoder compresses the
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 8
generic model
environment-speciﬁc
model
(b) testing procedures
per 
RoI 
FC+
activation
‘person’ 
‘running’ 
‘young’
whole
image 
convs
RoI 
pool 
FCx2
(1) detect object 
proposals
labeled image datasets 
action 
object 
attribute 
person
skateboarding
white
unlabeled data of
target environment
(2) extract features 
and scores
(3) detect 
abnormal events
(4) recount events
(a) learning procedures
(1) learn multi-task Fast R-CNN (generic model)
(2) learn anomaly detectors (environment-speciﬁc model)
Multi-task
Fast R-CNN
Multi-task 
Fast  R-CNN
fc7 features
classiﬁcation scores
if abnormal 
‘person’
‘walking’
object 
action 
attribute 
‘young’
‘dog’
‘running’
‘white’
Recounting result of 
…
…
object
action
attribute
learn normal behavior
fc7 model 
…
…
…
fc7 model 
→
 
running
 is the evidence 
to be abnormal
running
person
young
Detection result
abnormal RoI
anomaly score
predicted concepts
,…
,
Model building pipeline:
Exemplar 
Selection
Feature vectors for 
each video volume in 
a spatial region
Nominal video frames
concatenate
Exemplar 
feature 
vectors
V
ideo volume, 
t=1,N
V
ideo volume, 
t=N+1,2N
Anomaly detection 
pipeline :
Exemplar feature 
vectors for this 
spatial region
Test video frames
Distance 
to nearest 
neighbor
Scalar anomaly 
score for this 
video volume
Feature vector for 
this video volume
App.
Ang.
Mag.
Bkg_pix.
Bkg_cls.
Bkg_cls.
Bkg_cls.
Mag.
Ang.
App.
Fig. 5. Flowchart of four classical explainable V AD methods.
input sample into a latent space representation, significantly
reducing the dimensionality of the sample. The decoder re-
stores the input sample from the latent space representation,
increasing the dimensionality of the sample back to the
original input size. Due to its inherent image restoration and
high-level representation extraction capabilities, auto-encoder
is widely used in image restoration based pretext tasks for
self-supervised learning methods, such as reconstruction [3],
prediction [15], and inpainting [80]. Moreover, auto-encoder is
also used to extract features for one-class learning based meth-
ods [20], where the extracted features are used for optimizing
subsequent one-class classifiers. Auto-encoder structures are
highly flexible and can be based on various different base
networks, such as 2D CNN [3], 3D CNN [15], recurrent neural
network (RNN) [76], gated recurrent unit (GRU) [39], long
short-term memory (LSTM) [14], [101], graph convolutional
network (GCN) [42], [45] and Transformer [63], [80].
2) GAN: GAN has been widely adopted in various applica-
tions, including V AD, due to its powerful generative capabili-
ties. The key idea is to use the generator and discriminator to
identify abnormal sample that deviate from the learned normal
distribution. Specifically, GAN, like auto-encoder, is mainly
applied to image restoration based pretext tasks [4], [102]–
[105], where the generator creates restored images, and the
discriminator is discarded once the training is finished. On
the contrary, several one-class learning based methods [93],
[96] leverage the discriminator to assess the likelihood that a
new sample is real (normal) or generated (anomalous). Low
likelihood scores indicate anomalies, thereby achieving end-
to-end one-class classifiers.
3) Diffusion: Diffusion models have achieved state-of-the-
art performance across a wide range of generative tasks
and have become a promising research topic. Unlike tradi-
tional generative models like GAN or auto-encoder, diffusion
models generate samples by reversing a diffusion process
that gradually destroys sample structure. This reverse process
reconstructs the sample from noise in a step-by-step manner,
leading to high-quality results. Therefore, diffusion models
also appear in the image restoration based pretext tasks. Yan
et al. [7] and Flaborea et al. [49] introduced novel diffusion-
based methods to predict the features using the RGB frame
and skeleton respectively for V AD.
Ped1 Ped2
 AvenueFig. 6. Illustrations of frame-level (Top) and pixel-level (Bottom) output.
D. Model Refinement
1) Pseudo Anomalies: In semi-supervised learning, there is
usually a scarcity of real anomalous samples. To compensate
for this lack, several research studies opt to generate pseudo-
anomalies. Current approaches include: Perturbing normal
samples , i.e., applying random perturbations to normal video
samples, such as adding noise, shuffling frame sequences,
or adding extra patches [106]–[109]; Leveraging generative
models , i.e., using GAN or Diffusion to generate samples
similar to normal ones but with anomalous features [97],
[110]; Simulating specific anomalous behaviors , i.e., manually
introducing extra anomalous samples at the image-level or
feature-level [111], [112]. As a result, training with pseudo-
anomalous samples allows the detection model to learn a
variety of anomalous patterns, and helps the model learn a
broader range of anomalous features, improving its robustness
and generalization abilities in real-world applications.
2) Memory Bank: Memory banks [113]–[118] are used to
store feature representations of normal video samples, which
serve as a reference baseline and can be dynamically updated
to adapt to new normal patterns, thereby enabling the model to
better capture normal patterns, and simultaneously improving
the ability to adapt to changing environments. In specific im-
plementations, memory banks can be combined with different
network architectures, such as: auto-encoder based reconstruc-
tion (or prediction) [119]–[122] and contrastive learning [123].
E. Model Output
1) Frame-level: In frame-level output, each frame of the
video is classified as normal or abnormal. This output format
provides an overall view of which frames in the video contain
anomalies. Such output is simple and straightforward, easy
to implement and understand, and particularly effective for
detecting anomalies over a broad time range.
2) Pixel-level: In pixel-level output, not only is it identified
which frames contain anomalies, but also which specific pixel
regions within those frames are abnormal [126]. This output
format provides more granular information about the anoma-
lies. Pixel-level output offers precise locations and extents
of anomalies, providing more detailed information for further
analysis of the nature and cause of the anomalies. We illustrate
different model output in Figure 6.
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 9
Self-Supervised Learning One-Class Learning Interpretable Learning2017 2018 2019 2020 2021 2022 2023
FramePred
(Liu et al.)StackRNN
(Luo et al.)AdvOCC
(Hanson et al.)
DeepOC
(Wu et al.)MemAE
(Gong et al.)
ObjectAE
(Ionescu et al.)MPED-RNN
(Morais et al.)MNAD
(Park et al.)ClusterAE
(Chang et al.)
MultiTask
(Georgescu et al.)HF2-VAD 
(Liu et al.)SSPCAB
(Ristea et al.)
STJP
(Wang et al.)MPN
(Lv et al.)MoCoDAD
(Flaborea et al.)
PFMF
(Liu et al.)VEC
(Yu et al.)2016 2015AMDN
(Xu et al.)ConvAE
(Hasan et al.)
ConvLSTM
(Medel et al.)FRCN
(Hinami et al.)
STAE
(Zhao et al.)
AnomalyGAN
(Ravanbakhsh et al.)ST-GCE
(Markovitz1 et al.)HSC
(Sun et al.)EVAL
(Singh et al.)
Fig. 7. The chronology of representative semi-supervised V AD research.
TABLE II
QUANTITATIVE PERFORMANCE COMPARISON OF SEMI-SUPERVISED METHODS ON PUBLIC DATASETS .
Method Publication MethodologyPed1 Ped2 Avenue ShanghaiTech UBnormal
AUC (%) AUC (%) AUC (%) AUC (%) AUC (%)
AMDN [18] BMVC 2015 One-class classifier 92.1 90.8 - - -
ConvAE [3] CVPR 2016 Reconstruction 81.0 90.0 72.0 - -
STAE [15] ACMMM 2017 Hybrid 92.3 91.2 80.9 - -
StackRNN [74] ICCV 2017 Sparse coding - 92.2 81.7 68.0 -
FuturePred [4] CVPR 2018 Prediction 83.1 95.4 85.1 72.8 -
DeepOC [20] TNNLS 2019 One-class classifier 83.5 96.9 86.6 - -
MemAE [119] ICCV 2019 Reconstruction - 94.1 83.3 71.2 -
AnoPCN [81] ACMMM 2019 Prediction - 96.8 86.2 73.6 -
ObjectAE [25] CVPR 2019 One-class classifier - 97.8 90.4 84.9 -
BMAN [124] TIP 2019 Prediction - 96.6 90.0 76.2 -
sRNN-AE [76] TPAMI 2019 Sparse coding - 92.2 83.5 69.6 -
ClusterAE [52] ECCV 2020 Reconstruction - 96.5 86.0 73.3 -
MNAD [120] CVPR 2020 Reconstruction 97.0 88.5 70.5 -
VEC [62] ACMMM 2020 Cloze test - 97.3 90.2 74.8 -
AMMC-Net [33] AAAI 2021 Prediction - 96.6 86.6 73.7 -
MPN [121] CVPR 2021 Prediction 85.1 96.9 89.5 73.8 -
HF2-V AD [26] ICCV 2021 Hybrid - 99.3 91.1 76.2 -
BAF [111] TPAMI 2021 One-class classifier - 98.7 92.3 82.7 59.3
Multitask [85] CVPR 2021 Multiple tasks - 99.8 92.8 90.2 -
F2PN [31] TPAMI 2022 Prediction 84.3 96.2 85.7 73.0 -
DLAN-AC [122] ECCV 2022 Reconstruction - 97.6 89.9 74.7 -
BDPN [28] AAAI 2022 Prediction - 98.3 90.3 78.1 -
CAFE [114] ACMMM 2022 Prediction - 98.4 92.6 77.0 -
STJP [65] ECCV 2022 Jigsaw puzzle - 99.0 92.2 84.3 56.4
MPT [66] ICCV 2023 Multiple tasks - 97.6 90.9 78.8 -
HSC [30] CVPR 2023 Hybrid - 98.1 93.7 83.4
LERF [117] AAAI 2023 Predicition - 99.4 91.5 78.6 -
DMAD [115] CVPR 2023 Reconstruction - 99.7 92.8 78.8 -
EV AL [99] CVPR 2023 Interpretable learning - - 86.0 76.6 -
FBSC-AE [125] CVPR 2023 Prediction - - 86.8 79.2 -
FPDM [7] ICCV 2023 Prediction - - 90.1 78.6 62.7
PFMF [112] CVPR 2023 Multiple tasks - - 93.6 85.0 -
STG-NF [47] ICCV 2023 Gaussian classifier - - - 85.9 71.8
AED-MAE [80] CVPR 2024 Patch inpainting - 95.4 91.3 79.1 58.5
SSMCTB [79] TPAMI 2024 Patch inpainting - - 91.6 83.7 -
F . Performance Comparison
Figure 7 presents a concise chronology of semi-supervised
V AD methods. Besides, Table II provides a performance
summary observed in representative semi-supervised V AD
methods.
IV. W EAKLY SUPERVISED VIDEO ANOMALY DETECTION
Weakly supervised V AD is currently a highly regarded
research direction in the V AD field, with its origins traceable
to DeepMIL [5]. Compared to semi-supervised V AD, it is a
newer research direction, and therefore existing reviews lack a
comprehensive and in-depth introduction. As shown in Table I,
both Chandrakala et al. [12] and Liu et al. [13] mention theweakly supervised V AD task. However, the former only briefly
describes several achievements from 2018 to 2020, while the
latter, although encompassing recent works, lacks a scientific
taxonomy and simply categorizes them into single-modal and
multi-modal based on the different modalities. Given this
context, we survey related works from 2018 to the present,
including the latest methods based on pre-trained large models,
and we classify existing works from four aspects: model
input, methodology, refinement strategy, and model output.
The taxonomy of weakly supervised V AD is illustrated in
Figure 8.
Compared to semi-supervised V AD, weakly supervised
V AD explicitly defines anomalies during the training process,
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 10
I3D
3D-ResNet
TSNC3D
VideoSwinVGGish
One-Stage
MIL
Two-Stage
Self-TrainingMethodology Refinement Model Output Model Input
Frame Level
Pixel LevelOptical 
Flow
RGBAudio
TextHybrid
CLIPCLIPI3DTSN
Temporal 
Modeling
Spatio-Temporal 
Modeling
Modified MIL 
Metric Learning
Knowledge 
Distillation
Large Models
Fig. 8. The taxonomy of weakly supervised V AD. We provide a hierarchical
taxonomy that organizes existing deep weakly supervised V AD models by
model input, methodology, refinement strategy and model output into a
systematic framework.
giving the detection algorithm a clear direction. However, in
contrast to fully supervised V AD, the coarse weak supervision
signals introduce uncertainty into the detection process. Most
existing methods utilize the MIL mechanism to optimize the
model. This process can be viewed as selecting the hardest
regions (video clips) that appear most abnormal from normal
bags (normal videos) and the regions most likely to be abnor-
mal from abnormal bags (abnormal videos). Then, the goal
is to maximize the predicted confidence difference between
them (with the confidence for the hardest normal regions
approaching 0 and the confidence for the most abnormal
regions approaching 1), which can be regarded as a binary
classification optimization. By gradually mining all normal and
abnormal regions based on their different characteristics, the
anomaly confidence of abnormal regions increases while that
of normal regions decreases. Unfortunately, due to the lack
of strong supervision signals, the detection model inevitably
involves blind guessing in the above optimization process.
A. Model Input
Unlike semi-supervised V AD, the network input for weakly
supervised V AD is not the raw video, such as RGB, optical
flow, or skeleton. Instead, they are features extracted by pre-
trained models. This approach alleviates the problem posed by
the large scale of existing weakly supervised V AD datasets,
the diverse and complex scenes, and the weak supervision
signals. Using pre-trained features as input allows the effec-
tive utilization of off-the-shelf models’ learned knowledge of
appearance and motion, significantly reduces the complexity
of the detection model, and enables efficient training.
1) RGB: RGB is the most common model input. The gen-
eral approach divides a long video into multiple segments and
uses pre-trained visual models to extract global features from
each segment. As deep models continue to evolve and improve,
the visual models used have also been upgraded, progressing
from the initial C3D [5], [127], [128] to I3D [129]–[133], 3D-
ResNet [134], [135], TSN [136]–[138], and more recently, to
the popular Swin Transformer [139], [140] and CLIP [141],
[142]. This continuous upgrade in visual models has led to a
gradual improvement in detection performance.2) Optical Flow: Similar to RGB, the same approach is
applied to optical flow input to obtain corresponding global
features. However, due to the time-consuming nature of optical
flow extraction, it is less commonly used in existing methods.
Common pre-trained models for optical flow include I3D [143]
and TSN [137].
3) Audio: For multimodal datasets (e.g., XD-Violence) con-
taining audio signals, audio also holds significant perceptual
information. Unlike RGB images, audio is one-dimensional
and is typically processed as follows, audios are resampled,
compute the spectrograms, and create log mel spectrograms,
then these features are framed into non-overlapping examples.
Finally, these examples are then fed into a pre-trained audio
model, such as VGGish [144], to extract features [145], [146].
4) Text: More recently, several researchers [147]–[150]
attempt to incorporate text descriptions related to videos to aid
in V AD. These texts may be manually annotated or generated
by large models. The text data is typically converted into
features using the text encoder and then fed into the subsequent
detection network.
5) Hybrid: Common hybrid inputs include RGB combined
with optical flow [143], RGB combined with audio [151]–
[153], RGB combined with optical flow and audio [154], and,
more recently, RGB combined with text [155].
B. Methodology
Under the weak supervision, traditional fully supervised
methods are no longer adequate. To address this issue, we
identify two different approaches: the one-stage MIL and two-
stage self-learning.
1) One-stage MIL: The one-stage MIL [5], [156]–[158]
is the most commonly used approach for weakly supervised
V AD. The basic idea is to first divide long videos into multiple
segments, and then use a MIL mechanism to select the most
representative samples from these segments. This includes
selecting hard examples from normal videos that look most
like anomalies and the most likely abnormal examples from the
abnormal videos. The model is then optimized by lowering the
anomaly confidence of hard examples and increasing the con-
fidence of the most likely abnormal examples. Ultimately, the
confidence of model in predicting normal samples gradually
decreases, while its confidence in predicting abnormal samples
gradually increases, thereby achieving anomaly detection. The
advantage of this method lies in its simplicity and ease of
implementation. The MIL objective is showcased as,
lmil=X
imax (0 ,1−max Φ ( θ, xa
i) + max Φ ( θ, xn
i))
(14)
where xaandxndenote an abnormal video and a normal
video respectively.
Additionally, TopK [130] extends MIL by selecting the
topKsegments with the highest prediction scores from
each video, rather than just the highest-scoring segment, for
training. Therefore, MIL can be seen as a special case of TopK.
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 11
For these TopK segments, compute their average prediction
score as the predicted probability ˆy,
ˆy=σ
1
KKX
i∈topkΦ (θ, xi)
 (15)
where σis the sigmoid activation function. The cross-entropy
loss between ˆyand the label yis used to optimize the model,
ltopk=−(ylog(ˆy) + (1 −y) log(1 −ˆy)) (16)
The one-stage MIL mechanism leads to models that tend to
focus only on the most significant anomalies while ignoring
less obvious ones.
2) Two-stage Self-training: In contrast, improved two-stage
self-learning is more complex but also more effective. This
method employs a two-stage training process. First, a prelim-
inary model is pre-trained using the one-stage MIL. During
this phase, the model learns the basic principles of V AD.
Then, using the pre-trained model as an initial parameter, a
self-learning mechanism is introduced to adaptively train the
model further, enhancing its ability to recognize anomalies.
Specifically, during the self-learning phase, the predictions of
model from the pre-training stage are used to automatically
select high-confidence abnormal regions. These regions are
then treated as pseudo-labeled data to retrain the model,
thereby improving its ability to identify anomalies. This two-
stage training approach effectively enhances the performance
of model in weakly supervised V AD, further improving the
model’s generalization ability and robustness. NoiseClearner
[137], MIST [159], MSL [140], CUPL [160], and TPWNG
[161] are typical two-stage self-training works.
The two-stage self-learning method based on improved MIL
excels in weakly supervised V AD, but it also comes with some
drawbacks, such as, high computational complexity: the two-
stage training process requires more computational resources
and time. Both pre-training and self-learning phases involve
multiple iterations of training, leading to high computational
costs; Dependence on initial model quality: the self-learning
stage relies on the initial model generated during pre-training.
If the quality of the initial model is poor, erroneous predictions
may be treated as pseudo-labels, affecting subsequent training
effectiveness.
C. Refinement Strategy
Refinement strategies primarily focus on input features,
method design, and other aspects to compensate for the
shortcomings of weak supervision signals. We compile several
commonly used refinement strategies and provide a detailed
introduction in this section.
1) Temporal Modeling: Temporal modeling is essential
for capturing the critical context information in videos. Un-
like actions, anomalous events are complex combinations of
scenes, entities, actions, and other elements, which require
rich contextual information for accurate reasoning. Existing
temporal modeling methods can be broadly categorized into
local relationship modeling and global relationship modeling.
Local modeling is typically used for online detection [162],whereas global modeling is mainly used for offline detection
[163]. Techniques such as temporal convolutional networks
[130], [164], dilated convolution [163], GCN [165], [166],
conditional random field [167], and transformers [140], [168]–
[170] are frequently employed to capture these temporal
relationships effectively.
2) Spatio-temporal Modeling: Further, spatio-temporal
modeling can simultaneously capture spatial relationships,
highlighting anomalous spatial locations and effectively reduc-
ing noise from irrelevant backgrounds. This can be achieved
by segmenting video frames into multiple patches or using
existing object detectors to capture foreground objects. Then,
methods like self-attention [34], [135], [171], [172] are used
to learn the relationships between these patches or objects.
Compared to temporal modeling, spatio-temporal modeling
involves a higher computational load due to the increased
number of entities being analyzed.
3) MIL-based Refinement: The traditional MIL mechanism
focuses only on the segments with the highest anomaly scores,
which leads to a series of issues, such as ignoring event
continuity, the fixed length K-value not adapting to different
video scenarios, and bias towards abnormal snippets with sim-
ple contexts. Several advanced strategies [173], [174] aim to
address the limitations. By incorporating unbiased MIL [175],
prior information from text [149], [176], magnitude-level MIL
[177], continuity-aware refinement [178], and adaptive K-
values [179], the detection performance can be significantly
improved.
4) Feature Metric Learning: While MIL-based classifica-
tion ensures the interclass separability of features, this sep-
arability at the video level alone is insufficient for accurate
anomaly detection. In contrast, enhancing the discriminative
power of features through clustering similar features and iso-
lating different ones should complement and even augment the
separability achieved by MIL-based classification. Specifically,
the basic principle of feature metric learning is to make
similar features compact and different features distant in the
feature space to improve discrimination. Several works [132],
[147], [149], [162], [168], [180], [181] exploited feature metric
learning to enhance the feature discrimination.
5) Knowledge Distillation: Knowledge distillation aims to
transfer knowledge from the enriched branch to the barren
branch to alleviate the semantic gap, which is mainly applied
for modality-missing [182] or modality-enhancing [153] sce-
narios.
6) Leveraging Large Models: Large models have begun
to show tremendous potential and flexibility in the field of
V AD. They not only enhance detection capabilities through
vision-language features, e.g., CLIP-TSA [185] and cross-
modal semantic alignment, e.g., VadCLIP [183], but also lever-
age large language models to generate explanatory texts that
improve detection accuracy, e.g., TEV AD [148], UCA [155],
and V AD-Instruct50k [186]. Furthermore, they can directly use
the prior knowledge of large language models for training-
free V AD [187], [188], demonstrating advantages in rapid
deployment and cost reduction. Furthermore, the superior
zero-shot capabilities of these large models may be leveraged
for anomaly detection via various other ways, such as AD-
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 12
TABLE III
QUANTITATIVE PERFORMANCE COMPARISON OF WEAKLY SUPERVISED METHODS ON PUBLIC DATASETS .
Method Publication FeatureUCF-Crime XD-Violence ShanghaiTech TAD
AUC (%) AP (%) AUC (%) AUC (%)
DeepMIL [5] CVPR 2018 C3DRGB75.40 - - -
GCN [137] CVPR 2019 TSNRGB82.12 - 84.44 -
HLNet [130] ECCV 2020 I3DRGB82.44 75.41 - -
CLAWS [128] ECCV 2020 C3DRGB83.03 - 89.67 -
MIST [159] CVPR 2021 I3DRGB82.30 - 94.83 -
RTFM [163] ICCV 2021 I3DRGB84.30 77.81 97.21 -
CTR [162] TIP 2021 I3DRGB84.89 75.90 97.48 -
MSL [140] AAAI 2022 VideoSwinRGB85.62 78.59 97.32 -
S3R [131] ECCV 2022 I3DRGB85.99 80.26 97.48 -
SSRL [171] ECCV 2022 I3DRGB87.43 - 97.98 -
CMRL [166] CVPR 2023 I3DRGB86.10 81.30 97.60 -
CUPL [160] CVPR 2023 I3DRGB86.22 81.43 - 91.66
MGFN [177] AAAI 2023 VideoSwinRGB86.67 80.11 - -
UMIL [175] CVPR 2023 CLIP 86.75 - - 92.93
DMU [170] AAAI 2023 I3DRGB86.97 81.66 - -
PE-MIL [176] CVPR 2024 I3DRGB86.83 88.05 98.35 -
TPWNG [161] CVPR 2024 CLIP 87.79 83.68 - -
VadCLIP [183] AAAI 2024 CLIP 88.02 84.51 - -
STPrompt [184] ACMMM 2024 CLIP 88.08 - 97.81 -
One-Stage MIL Two-Stage Self-Training2018 2019 2020 2021 2022 2023DeepMIL
(Sultani et al.)
MAF
(Zhu et al.)Background-Bias
(Liu et al.)
GCN
(Zhong et al.)CLAWS
(Zaheer et al.)HLNet
(Wu et al.)
MIST
(Feng et al.) 
SSRL
(Li et al.)MSL
(Li et al.)
CTRFD
(Wu et al.)CRFAD
(Purwanto et al.)CUPL
(Zhang et al.)
DMU
(Zhou et al.)2024RTFM
(Tian et al.)
UMIL
(Lv et al.)PKD
(Liu et al.)VadCLIP
(Wu et al.)
PEL4VAD
(Pu et al.)
TPWNG
(Yang et al.)
Fig. 9. The Chronology for representative weakly supervised V AD research.
oriented prompts [189], [190] or generic residual learning
[191]. These methods collectively advance the development
of V AD technology, providing new avenues and tools for
achieving more efficient and interpretable V AD.
D. Model Output
1) Frame-level: Similar to semi-supervised V AD, the out-
put in weakly supervised V AD is typically frame-level pre-
diction results, indicating the probability of each frame being
anomalous. This type of output is intuitive and straightforward,
which is why it is commonly adopted.
2) Pixel-level: Although frame-level output is intuitive, it
lacks interpretability. Therefore, some works have begun to fo-
cus on achieving pixel-level detection. For instance, Liu et al.
[192] used spatial-level strong supervision signals to achieve
spatial localization. Wu et al. [193] took a different approach
by not relying on labor-intensive annotations. Instead, they
leveraged algorithms such as object detection and tracking,
drawing inspiration from video spatio-temporal localization
algorithms, and achieved anomaly spatio-temporal localization
through spatio-temporal object tube analysis.E. Performance Comparison
As depicted in Figure 9, several significant research works
have emerged in the field’s chronology. Moreover, We present
an elaborate performance comparison of existing research, as
detailed in Table III.
V. F ULLY SUPERVISED VIDEO ANOMALY DETECTION
Fully supervised V AD refers to the task of detecting video
anomalies under the condition that the dataset has detailed
frame-level or video-level annotations. Here, we consider
video violence detection as a fully supervised V AD task.
A. Approach Categorization
Video violence detection typically takes the appearance,
motion, skeleton, audio, or a combination of these as the
input. It can be categorized based on the type of input into
the following types:
Appearance input mainly consists of raw RGB images, di-
rectly showcasing the visual effect of video frames. This helps
the model better understand anomalies that can be directly
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 13
TABLE IV
QUANTITATIVE PERFORMANCE COMPARISON OF FULLY SUPERVISED METHODS ON PUBLIC DATASETS .
Method Publication Model InputHockey Fights Violent-Flows RWF-2000 Crowed Violence
Accuracy (%) Accuracy (%) Accuracy (%) Accuracy (%)
TS-LSTM [194] PR 2016 RGB+Flow 93.9 - - -
FightNet [195] JPCS 2017 RGB+Flow 97.0 - - -
ConvLSTM [199] A VSS 2017 Frame Difference 97.1 94.6 - -
BiConvLSTM [200] ECCVW 2018 Frame Difference 98.1 96.3 - -
SPIL [201] ECCV 2020 Skeleton 96.8 - 89.3 94.5
FlowGatedNet [203] ICPR 2020 RGB+Flow 98.0 - 87.3 88.9
X3D [206] A VSS 2022 RGB - 98.0 94.0 -
HSCD [205] CVIU 2023 Skeleton+Frame Difference 94.5 - 90.3 94.3
TABLE V
QUANTITATIVE PERFORMANCE COMPARISON OF UNSUPERVISED METHODS ON PUBLIC DATASETS .
Method Publication MethodologyAvenue Subway Exit Ped1 Ped2 ShaihaiTech UMN
AUC (%) AUC (%) AUC (%) AUC (%) AUC (%) AUC (%)
ADF [207] ECCV 2016 Change detection 78.3 82.4 - - - 91.0
Unmasking [208] ICCV 2017 Change detection 80.6 86.3 68.4 82.2 - 95.1
MC2ST [209] BMVC 2018 Change detection 84.4 93.1 71.8 87.5 - -
DAW [210] ACMMM 2018 Pseudo label 85.3 84.5 77.8 96.4 - -
STDOR [211] CVPR 2020 Pseudo label - 92.7 71.7 83.2 - 97.4
TMAE [212] ICME 2022 Change detection 89.8 - 75.7 94.1 71.4 -
CIL [213] AAAI 2022 Others 90.3 97.6 84.9 99.4 - 100
LBR-SPR [214] CVPR 2022 Others 92.8 - 81.1 97.2 72.6 -
detected from a visual perspective. Many methods [194]–
[197] used RGB features extracted from raw images using
pre-trained models as the model input.
Motion input mainly includes optical flow, optical flow accel-
eration, and frame differences. These inputs directly showcase
the motion state of objects, helping to identify anomalies
from the motion perspective that might be difficult to detect
visually. Dong et al. [194] and Bruno Peixoto et al. [198]
used optical flow and optical flow acceleration as input, while
Sudhakaran et al. [199] and Hanson et al. [200] employed
frame differences as model input.
Skeleton input can intuitively display the pose state of hu-
mans, allowing the model to exclude background interference
and focus on human actions. This enables more intuitive and
vivid recognition of violent behavior. Su et al. [201] and
Singh et al. [202] conducted violence detection by studying
the interaction relationships between skeletal points.
Audio input can provide additional information to aid in
identifying violent events [198]. This is because certain violent
incidents inevitably involve changes in sound, such variations
help us better detect violent events, especially when RGB
images may not effectively detect due to issues like occlusion.
Hybrid input combines the strengths of different modalities to
better detect violent events. Cheng et al. [203] utilized RGB
images and optical flow as input, while Shang et al. [204]
combined RGB images with audio as input. Garcia et al. [205]
fed skeleton and frame differences into detection models.
B. Performance Comparison
We present the performance comparison of existing fully
supervised V AD research in Table IV.
VI. U NSUPERVISED VIDEO ANOMALY DETECTION
Despite the great popularity of supervised V AD, supervised
methods still have shortcomings in practical applications. Onthe one hand, we cannot clearly define what constitutes normal
behavior of real-life human activities in many cases, e.g.,
running in a sports ground is normal but running in a library is
forbidden. On the other hand, it is impractical to know every
possible normal event in advance, especially for scientific
research. Therefore, V AD in unsupervised environments is of
significant research value.
A. Approach Categorization
Through an in-depth investigation, we roughly classify the
current unsupervised V AD methods into 3 categories: pseudo
label, change detection, and others.
Pseudo label based paradigm is described as follows.
Wang et al. [210] proposed a two-stage training approach
where an auto-encoder is first trained with an adaptive re-
construction loss threshold to estimate normal events from
unlabeled videos. These estimated normal events are then
used as pseudo-labels to train an OC-SVM, refining the
normality model to exclude anomalies and improve detection
performance. Pang et al. [211] introduced a self-training
deep ordinal regression method, starting with initial detection
using classical one-class algorithms to generate pseudo-labels
for anomalous and normal frames. An end-to-end anomaly
score learner is then trained iteratively using a self-training
strategy that optimizes the detector with newly generated
pseudo labels. Zaheer et al. [215] proposed an unsupervised
generative cooperative learning approach, leveraging the low-
frequency nature of anomalies for cross-supervision between a
generator and a discriminator, each learning from the pseudo-
labels of the other. Al-lahham et al. [216] presented a coarse-
to-fine pseudo-label generation framework using hierarchical
divisive clustering for coarse pseudo-labels at the video level
and statistical hypothesis testing for fine pseudo-labels at the
segment level, training the anomaly detector with the obtained
pseudo-labels.
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 14
Change detection based paradigm can be summarized as
follows. Del Giorno et al. [207] performed change detection
in video frames using a simple logistic regression classifier
to measure deviations between the data, making comparisons
time-independent by randomly ordering frames. Ionescu et al.
[208] proposed a change detection framework based on the
unmasking technique, determining abnormal events by observ-
ing changes in classifier accuracy between consecutive events.
Liu et al. [209] linked the heuristic unmasking procedure
to multiple classifier two-sample tests in statistical machine
learning, aiming to improve the unmasking method. Hu et al.
[212] introduced a method based on masked auto-encoders
[217], where the rare and unusual nature of anomalies leads
to poor prediction of changing events, allowing for anomaly
detection and scoring in unlabeled videos.
Other paradigm includes the below methods. Li et al. [218]
proposed a clustering technique, where an auto-encoder is
trained on a subset of normal data and iterates between
hypothetical normal candidates based on clustering and rep-
resentation learning. The reconstruction error is used as a
scoring function to assess normality. Lin et al. [213] intro-
duced a causal inference framework to reduce the effect of
noisy pseudo-labeling, combining long-term temporal context
with local image context for anomaly detection. Yu et al.
[214] highlighted the effectiveness of deep reconstruction for
unsupervised V AD, revealing a normality advantage where
normal events have lower reconstruction loss. They integrated
a novel self-paced refinement scheme into localization-based
reconstruction for unsupervised V AD.
B. Performance Comparison
we present the performance comparison of existing unsu-
pervised V AD research in Table V.
VII. O PEN-SETSUPERVISED VIDEO ANOMALY
DETECTION
It is challenging to make well-trained supervised model
deployed in the open world detect unseen anomalies. Unseen
anomalies are highly likely to occur in real-world scenarios,
thus research on open-set anomaly detection has garnered sig-
nificant attention. Open-set supervised V AD is a challenging
task where the goal is to detect anomalous events in videos
that are unseen during the training phase. Unlike traditional
(closed-set) V AD, where the types of anomalies are known
and well-defined, open-set V AD must handle unforeseen and
unknown anomalies. This is crucial for real-world applications,
as it is impractical to anticipate and annotate every possible
anomaly during training. Therefore, research on open-set V AD
has garnered significant attention. However, existing review
works lack an investigation into open-set V AD. Based on
this, we conduct an in-depth survey and make a systematic
taxonomy of existing open-set V AD works. To our knowledge,
this is the first review that includes a detailed introduction
to open-set supervised V AD. In this section, we broadly
categorize open-set supervised V AD into two types based on
different research directions: open-set V AD and few-set V AD.
In Figure 10, we showcase six classical open-set supervised
V AD methods.A. Open-set VAD
Open-set V AD is a vital area of research that addresses
the limitations of traditional anomaly detection methods by
focusing on the detection of unknown anomalies. MLEP [219],
as the first work on open-set supervised V AD, addressed
the challenge of detecting anomalies in videos when only a
few anomalous examples are available for training. It empha-
sizes learning a margin that separates normal and anomalous
samples in the feature space. This helps in distinguishing
anomalies even when only a few examples are available. The
follow-up work [220] introduced a new benchmark, namely
UBnormal, specifically designed for open-set V AD. It aims to
provide a comprehensive evaluation framework for testing and
comparing various V AD methods under open-set conditions.
Zhu et al. [221] broke through the closed-set detection limi-
tations and developed novel techniques that can generalize to
previously unseen anomalies and effectively distinguish them
from normal events. Specially, a normalizing flow model is
introduced to create pseudo anomaly features. Recently, Wu
et al. [142] extended open-set V AD to the more challenging
open-vocabulary V AD, aiming to both detect and recognize
anomaly categories. Centered around vision-language models,
this task is achieved by matching videos with correspond-
ing textual labels. Additionally, large generative models and
language models are utilized to generate pseudo-anomalous
samples. There are other methods for the open-set setting,
such as [222], [223], but they are focused on image-level
anomaly detection. Through various innovative approaches,
such as margin learning, the development of benchmarks,
generalization strategies, and the integration of language-
vision models, researchers are pushing the boundaries of what
is possible in V AD. These advancements are paving the way
for more robust, flexible, and practical V AD systems suitable
for a wide range of real-world applications.
B. Few-shot VAD
The goal of few-shot V AD is to detect anomalies in a
previously unseen scene with only a few frames. Compared
to open-set V AD, the main difference lies in that a few real
frames of unseen anomalies are provided. This task is first
introduced by Lu et al. [224], and a meta-learning based model
is proposed to tackle this problem. During the test stage, the
model needs to be fine-tuned via a few provided samples from
the new scene. In order to avoid extra fine-tuning processes
before deployment, Hu et al. [225] and Huang et al. [226]
adopted the metric-based adaptive network and variational
network respectively, both leveraging few normal samples as
the reference during the test stage without any fine-tuning.
Further, Aich et al. [227] presented a novel zxV AD framework,
a significant advancement by enabling anomaly detection
across domains without requiring target domain adaptation.
In this work, a novel untrained CNN based anomaly synthesis
module crafts pseudo-abnormal examples by adding foreign
objects in normal video frames in a training-free manner.
This contrasts with the above few-shot adaptive methods,
which require a few labeled data from the target domain for
fine-tuning. The former focuses on domain-invariant feature
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 15
Fig. 10. Flowchart of six classical open-set supervised V AD methods. Top: open-set methods; Bottom: few-shot methods.
extraction and unsupervised learning, ensuring robustness and
generalizability, while the latter relies on few-shot learning to
adapt models to new domains with minimal labeled data.
VIII. F UTURE OPPORTUNITIES
A. Creating Comprehensive Benchmarks
The current V AD benchmarks have various limitations in
terms of data size, modality, and capturing views. Thus, an
important future direction is to extend benchmarks along these
dimensions for providing more realistic V AD test platforms.
1) Large-scale: Currently, in V AD—especially in semi-
supervised V AD—the data scale is too small. For example,
the UCSD Ped dataset [228] lasts only a few minutes, and
even the larger ShanghaiTech dataset [14] is only a few hours
long. Compared to datasets in video action recognition tasks
[229], which can last hundreds or thousands of hours, V AD
datasets are extremely small. This is far from sufficient for
training V AD models, as training on small-scale datasets is
highly prone to over-fitting in large models. While this might
lead to good detection results on the small-scale test data, it
can severely impact the performance of V AD models intended
for real-world deployment. Therefore, expanding the data scale
is a key focus of future research.
2) Multi-modal: Currently, there is limited research on
multimodal V AD. Just as humans perceive the world through
multiple senses [230], such as vision, hearing, and smell,
effectively utilizing various modality information in the face of
multi-source heterogeneous data can enhance the performance
of anomaly detection. For example, using audio information
can better detect anomalies such as screams and panic, while
using infrared information can identify abnormal situations in
dark environments.
3) Egocentric, Multi-view, 3D, etc.: Egocentric V AD in-
volves using data captured from wearable devices or body-
mounted cameras to simulate how individuals perceive their
environment and identify abnormal events, such as detecting
falls or aggressive behavior in real time. Creating multi-view
benchmarks that leverage data from viewpoints allows for acomprehensive environment analysis, enabling the detection of
anomalies that may not be visible from a single perspective.
3D perspectives from depth information or point clouds can
offer more detailed spatial information, enabling models to
better understand the structure and context of the environment,
which also brings multi-modal signals.
B. Towards Open-world Task
The current research focuses on the closed-set V AD, which
is restricted to detecting only those anomalies that are defined
and annotated during training. In applications like urban
surveillance, the inability to adapt to unforeseen anomalies
limits the practicality and effectiveness of closed-set V AD
models. Therefore, moving towards the open-world V AD
task, handling the uncertainty and variability of real-world
situations, is a feasible future trend. To accomplish this task,
several key approaches and their combination can be taken
into account. Self-supervised learning: leveraging unlabeled
data to learn discriminative representations that can distinguish
between normal and abnormal events [231]; Open-vocabulary
learning: developing models that can adapt to new anomalies
with large models [142], pseudo anomaly synthesis, or min-
imal labeled examples; Incremental learning: continuously
updating models with new data and anomaly types without
forgetting previously learned information [232].
C. Embracing Pre-trained Large models
Pre-trained large models have shown remarkable success
in various computer vision tasks, and these models can be
leveraged in V AD to enhance the understanding and detection
of anomalies by integrating semantic context and improving
feature representations. Here are several feasible directions.
Feature extraction: pre-trained weights of large models,
which have been trained on large-scale datasets, provide
a strong foundation for feature extraction and reduce the
need for extensive training from scratch [185]. Semantic
understanding: language-vision models can be utilized to
understand and incorporate contextual information from video
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 16
scenes. For instance, text descriptions associated with video
frames can provide additional context that helps in identifying
anomalies. In the same way, leverage the language capabilities
of these models to generate or understand textual descriptions
of anomalies, aiding in both the detection and interpretation
of the anomalies [186]. Zero-shot learning: exploit the zero-
shot learning capabilities of language-vision models to detect
anomalies without requiring explicit examples during training.
This is particularly useful in open-set V AD scenarios where
new types of anomalies can occur [190].
D. Exploiting Interpretable VAD
Interpretable V AD focuses on creating models that not only
detect anomalies but also provide understandable explanations
for their predictions. This is crucial for gaining trust in the
system, especially in high-stakes applications like surveillance,
healthcare, and autonomous vehicles. Here are several feasi-
ble directions from three different layers of a V AD system.
Input: instead of directly inputting raw video data into the
model, leverage existing technologies to extract key informa-
tion, such as foreground objects, position coordinates, motion
trajectories, and crowd relationships. Algorithm: combining
algorithms from different domains can be helpful for enhanced
reasoning, including: knowledge graphs, i.e., utilize knowledge
graphs to incorporate contextual information and relationships
between entities; intent prediction, i.e., use intent prediction
algorithms to anticipate future actions and detect deviations
from expected behaviors [125]. LLMs’ reasoning, i.e., the
textual descriptions of detected anomalies using large LLMs
can also be used for the explanation. These descriptions can
explain what the model perceives as abnormal and why [186].
Output: Various aspects such as the spatio-temporal changes
and patterns in the video may be synthesized to explain
anomalies [184].
IX. C ONCLUSION
We present a comprehensive survey of video anomaly
detection approaches in the deep learning era. Unlike previous
reviews mainly focusing on semi-supervised video anomaly
detection, we provide a taxonomy that systematically divides
the existing works into five categories by their supervision
signals, e.g., semi-supervised, weakly supervised, unsuper-
vised, fully supervised, and open-set supervised video anomaly
detection. For each category, we further refine the categories
based on model differences, e.g., model input and output,
methodology, refinement strategy, and architecture, and we
demonstrate the performance comparison of various methods.
Finally, we discuss several promising research directions for
deep learning based video anomaly detection in the future.
REFERENCES
[1] G. Pang, C. Shen, L. Cao, and A. V . D. Hengel, “Deep learning for
anomaly detection: a review,” ACM Computing Surveys , vol. 54, no. 2,
pp. 1–38, 2021.
[2] Y . Yao, X. Wang, M. Xu, Z. Pu, Y . Wang, E. Atkins, and D. J. Crandall,
“Dota: unsupervised detection of traffic anomaly in driving videos,”
IEEE Transactions on Pattern Analysis and Machine Intelligence ,
vol. 45, no. 1, pp. 444–459, 2022.[3] M. Hasan, J. Choi, J. Neumann, A. K. Roy-Chowdhury, and L. S.
Davis, “Learning temporal regularity in video sequences,” in Pro-
ceedings of the IEEE Conference on Computer Vision and Pattern
Recognition , 2016, pp. 733–742.
[4] W. Liu, W. Luo, D. Lian, and S. Gao, “Future frame prediction
for anomaly detection–a new baseline,” in Proceedings of the IEEE
Conference on Computer Vision and Pattern Recognition , 2018, pp.
6536–6545.
[5] W. Sultani, C. Chen, and M. Shah, “Real-world anomaly detection
in surveillance videos,” in Proceedings of the IEEE Conference on
Computer Vision and Pattern Recognition , 2018, pp. 6479–6488.
[6] C. Lu, J. Shi, and J. Jia, “Abnormal event detection at 150 fps in
matlab,” in Proceedings of the IEEE International Conference on
Computer Vision , 2013, pp. 2720–2727.
[7] C. Yan, S. Zhang, Y . Liu, G. Pang, and W. Wang, “Feature prediction
diffusion model for video anomaly detection,” in Proceedings of the
IEEE International Conference on Computer Vision , 2023, pp. 5527–
5537.
[8] B. Ramachandra, M. J. Jones, and R. R. Vatsavai, “A survey of single-
scene video anomaly detection,” IEEE Transactions on Pattern Analysis
and Machine Intelligence , vol. 44, no. 5, pp. 2293–2312, 2020.
[9] K. K. Santhosh, D. P. Dogra, and P. P. Roy, “Anomaly detection in road
traffic using visual surveillance: a survey,” ACM Computing Surveys ,
vol. 53, no. 6, pp. 1–26, 2020.
[10] R. Nayak, U. C. Pati, and S. K. Das, “A comprehensive review on
deep learning-based methods for video anomaly detection,” Image and
Vision Computing , vol. 106, p. 104078, 2021.
[11] T. M. Tran, T. N. Vu, N. D. V o, T. V . Nguyen, and K. Nguyen,
“Anomaly analysis in images and videos: a comprehensive review,”
ACM Computing Surveys , vol. 55, no. 7, pp. 1–37, 2022.
[12] S. Chandrakala, K. Deepak, and G. Revathy, “Anomaly detection in
surveillance videos: a thematic taxonomy of deep models, review and
performance analysis,” Artificial Intelligence Review , vol. 56, no. 4, pp.
3319–3368, 2023.
[13] Y . Liu, D. Yang, Y . Wang, J. Liu, J. Liu, A. Boukerche, P. Sun,
and L. Song, “Generalized video anomaly event detection: systematic
taxonomy and comparison of deep models,” ACM Computing Surveys ,
vol. 56, no. 7, 2023.
[14] W. Luo, W. Liu, and S. Gao, “Remembering history with convolutional
lstm for anomaly detection,” in Proceedings of the IEEE International
Conference on Multimedia and Expo , 2017, pp. 439–444.
[15] Y . Zhao, B. Deng, C. Shen, Y . Liu, H. Lu, and X.-S. Hua, “Spatio-
temporal autoencoder for video anomaly detection,” in Proceedings of
the ACM International Conference on Multimedia , 2017, pp. 1933–
1941.
[16] M. Ravanbakhsh, M. Nabi, E. Sangineto, L. Marcenaro, C. Regazzoni,
and N. Sebe, “Abnormal event detection in videos using generative
adversarial nets,” in Proceedings of the IEEE International Conference
on Image Processing , 2017, pp. 1577–1581.
[17] T.-N. Nguyen and J. Meunier, “Anomaly detection in video sequence
with appearance-motion correspondence,” in Proceedings of the IEEE
International Conference on Computer Vision , 2019, pp. 1273–1283.
[18] D. Xu, E. Ricci, Y . Yan, J. Song, and N. Sebe, “Learning deep repre-
sentations of appearance and motion for anomalous event detection,”
inProceedings of the British Machine Vision Conference , 2015.
[19] D. Xu, Y . Yan, E. Ricci, and N. Sebe, “Detecting anomalous events
in videos by learning deep representations of appearance and motion,”
Computer Vision and Image Understanding , vol. 156, pp. 117–127,
2017.
[20] P. Wu, J. Liu, and F. Shen, “A deep one-class neural network for
anomalous event detection in complex scenes,” IEEE Transactions on
Neural Networks and Learning Systems , vol. 31, no. 7, pp. 2609–2622,
2019.
[21] M. Sabokrou, M. Fayyaz, M. Fathy, and R. Klette, “Deep-cascade:
cascading 3d deep neural networks for fast anomaly detection and lo-
calization in crowded scenes,” IEEE Transactions on Image Processing ,
vol. 26, no. 4, pp. 1992–2004, 2017.
[22] T. Wang, M. Qiao, Z. Lin, C. Li, H. Snoussi, Z. Liu, and C. Choi,
“Generative neural networks for anomaly detection in crowded scenes,”
IEEE Transactions on Information Forensics and Security , vol. 14,
no. 5, pp. 1390–1399, 2018.
[23] Y . Fan, G. Wen, D. Li, S. Qiu, M. D. Levine, and F. Xiao, “Video
anomaly detection and localization via gaussian mixture fully convo-
lutional variational autoencoder,” Computer Vision and Image Under-
standing , vol. 195, p. 102920, 2020.
[24] R. Hinami, T. Mei, and S. Satoh, “Joint detection and recounting of
abnormal events by learning deep generic knowledge,” in Proceedings
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 17
of the IEEE International Conference on Computer Vision , 2017, pp.
3619–3627.
[25] R. T. Ionescu, F. S. Khan, M.-I. Georgescu, and L. Shao, “Object-
centric auto-encoders and dummy anomalies for abnormal event detec-
tion in video,” in Proceedings of the IEEE Conference on Computer
Vision and Pattern Recognition , 2019, pp. 7842–7851.
[26] Z. Liu, Y . Nie, C. Long, Q. Zhang, and G. Li, “A hybrid video anomaly
detection framework via memory-augmented flow reconstruction and
flow-guided frame prediction,” in Proceedings of the IEEE Interna-
tional Conference on Computer Vision , 2021, pp. 13 588–13 597.
[27] Q. Bao, F. Liu, Y . Liu, L. Jiao, X. Liu, and L. Li, “Hierarchical
scene normality-binding modeling for anomaly detection in surveil-
lance videos,” in Proceedings of the ACM International Conference on
Multimedia , 2022, pp. 6103–6112.
[28] C. Chen, Y . Xie, S. Lin, A. Yao, G. Jiang, W. Zhang, Y . Qu, R. Qiao,
B. Ren, and L. Ma, “Comprehensive regularization in a bi-directional
predictive network for video anomaly detection,” in Proceedings of the
AAAI Conference on Artificial Intelligence , vol. 36, no. 1, 2022, pp.
230–238.
[29] C. Sun, Y . Jia, and Y . Wu, “Evidential reasoning for video anomaly
detection,” in Proceedings of the ACM International Conference on
Multimedia , 2022, pp. 2106–2114.
[30] S. Sun and X. Gong, “Hierarchical semantic contrast for scene-aware
video anomaly detection,” in Proceedings of the IEEE Conference on
Computer Vision and Pattern Recognition , 2023, pp. 22 846–22 856.
[31] W. Luo, W. Liu, D. Lian, and S. Gao, “Future frame prediction network
for video anomaly detection,” IEEE Transactions on Pattern Analysis
and Machine Intelligence , vol. 44, no. 11, pp. 7505–7520, 2021.
[32] P. Wu, J. Liu, M. Li, Y . Sun, and F. Shen, “Fast sparse coding networks
for anomaly detection in videos,” Pattern Recognition , vol. 107, p.
107515, 2020.
[33] R. Cai, H. Zhang, W. Liu, S. Gao, and Z. Hao, “Appearance-motion
memory consistency network for video anomaly detection,” in Proceed-
ings of the AAAI Conference on Artificial Intelligence , vol. 35, no. 2,
2021, pp. 938–946.
[34] Y . Liu, J. Liu, X. Zhu, D. Wei, X. Huang, and L. Song, “Learning
task-specific representation for video anomaly detection with spatial-
temporal attention,” in Proceedings of the IEEE International Confer-
ence on Acoustics, Speech, and Signal Processing , 2022, pp. 2190–
2194.
[35] X. Huang, C. Zhao, and Z. Wu, “A video anomaly detection framework
based on appearance-motion semantics representation consistency,”
inProceedings of the IEEE International Conference on Acoustics,
Speech, and Signal Processing , 2023, pp. 1–5.
[36] N. Li, F. Chang, and C. Liu, “Spatial-temporal cascade autoencoder
for video anomaly detection in crowded scenes,” IEEE Transactions
on Multimedia , vol. 23, pp. 203–215, 2020.
[37] B. Ramachandra, M. Jones, and R. Vatsavai, “Learning a distance
function with a siamese network to localize anomalies in videos,”
inProceedings of the IEEE Winter Conference on Applications of
Computer Vision , 2020, pp. 2598–2607.
[38] T. Reiss and Y . Hoshen, “Attribute-based representations for ac-
curate and interpretable video anomaly detection,” arXiv preprint
arXiv:2212.00789 , 2022.
[39] R. Morais, V . Le, T. Tran, B. Saha, M. Mansour, and S. Venkatesh,
“Learning regularity in skeleton trajectories for anomaly detection in
videos,” in Proceedings of the IEEE Conference on Computer Vision
and Pattern Recognition , 2019, pp. 11 996–12 004.
[40] A. Markovitz, G. Sharir, I. Friedman, L. Zelnik-Manor, and S. Avi-
dan, “Graph embedded pose clustering for anomaly detection,” in
Proceedings of the IEEE Conference on Computer Vision and Pattern
Recognition , 2020, pp. 10 539–10 547.
[41] R. Rodrigues, N. Bhargava, R. Velmurugan, and S. Chaudhuri, “Multi-
timescale trajectory prediction for abnormal human activity detection,”
inProceedings of the IEEE Winter Conference on Applications of
Computer Vision , 2020, pp. 2626–2634.
[42] W. Luo, W. Liu, and S. Gao, “Normal graph: spatial temporal graph
convolutional networks based prediction network for skeleton based
video anomaly detection,” Neurocomputing , vol. 444, pp. 332–337,
2021.
[43] X. Zeng, Y . Jiang, W. Ding, H. Li, Y . Hao, and Z. Qiu, “A hierarchical
spatio-temporal graph convolutional neural network for anomaly detec-
tion in videos,” IEEE Transactions on Circuits and Systems for Video
Technology , vol. 33, no. 1, pp. 200–212, 2021.
[44] Y . Yang, Z. Fu, and S. M. Naqvi, “A two-stream information fusion
approach to abnormal event detection in video,” in Proceedings ofthe IEEE International Conference on Acoustics, Speech, and Signal
Processing , 2022, pp. 5787–5791.
[45] N. Li, F. Chang, and C. Liu, “Human-related anomalous event detection
via spatial-temporal graph convolutional autoencoder with embedded
long short-term memory network,” Neurocomputing , vol. 490, pp. 482–
494, 2022.
[46] C. Huang, Y . Liu, Z. Zhang, C. Liu, J. Wen, Y . Xu, and Y . Wang,
“Hierarchical graph embedded pose regularity learning via spatio-
temporal transformer for abnormal behavior detection,” in Proceedings
of the ACM International Conference on Multimedia , 2022, pp. 307–
315.
[47] O. Hirschorn and S. Avidan, “Normalizing flows for human pose
anomaly detection,” in Proceedings of the IEEE International Con-
ference on Computer Vision , 2023, pp. 13 545–13 554.
[48] S. Yu, Z. Zhao, H. Fang, A. Deng, H. Su, D. Wang, W. Gan, C. Lu,
and W. Wu, “Regularity learning via explicit distribution modeling for
skeletal video anomaly detection,” IEEE Transactions on Circuits and
Systems for Video Technology , pp. 1–1, 2023.
[49] A. Flaborea, L. Collorone, G. M. D. Di Melendugno, S. D’Arrigo,
B. Prenkaj, and F. Galasso, “Multimodal motion conditioned diffusion
model for skeleton-based video anomaly detection,” in Proceedings
of the IEEE International Conference on Computer Vision , 2023, pp.
10 318–10 329.
[50] A. Stergiou, B. De Weerdt, and N. Deligiannis, “Holistic representation
learning for multitask trajectory anomaly detection,” in Proceedings of
the IEEE Winter Conference on Applications of Computer Vision , 2024,
pp. 6729–6739.
[51] R. Pi, P. Wu, X. He, and Y . Peng, “Eogt: video anomaly detection
with enhanced object information and global temporal dependency,”
ACM Transactions on Multimedia Computing, Communications and
Applications , 2024.
[52] Y . Chang, Z. Tu, W. Xie, and J. Yuan, “Clustering driven deep autoen-
coder for video anomaly detection,” in Proceedings of the European
Conference on Computer Vision , 2020, pp. 329–345.
[53] Z. Fang, J. Liang, J. T. Zhou, Y . Xiao, and F. Yang, “Anomaly detection
with bidirectional consistency in videos,” IEEE Transactions on Neural
Networks and Learning Systems , vol. 33, no. 3, pp. 1079–1092, 2020.
[54] C. Huang, Z. Yang, J. Wen, Y . Xu, Q. Jiang, J. Yang, and Y . Wang,
“Self-supervision-augmented deep autoencoder for unsupervised visual
anomaly detection,” IEEE Transactions on Cybernetics , vol. 52, no. 12,
pp. 13 834–13 847, 2021.
[55] J. T. Zhou, L. Zhang, Z. Fang, J. Du, X. Peng, and Y . Xiao, “Attention-
driven loss for anomaly detection in video surveillance,” IEEE Trans-
actions on Circuits and Systems for Video Technology , vol. 30, no. 12,
pp. 4639–4647, 2019.
[56] Y . Zhang, X. Nie, R. He, M. Chen, and Y . Yin, “Normality learning
in multispace for video anomaly detection,” IEEE Transactions on
Circuits and Systems for Video Technology , vol. 31, no. 9, pp. 3694–
3706, 2020.
[57] X. Wang, Z. Che, B. Jiang, N. Xiao, K. Yang, J. Tang, J. Ye,
J. Wang, and Q. Qi, “Robust unsupervised video anomaly detection by
multipath frame prediction,” IEEE Transactions on Neural Networks
and Learning Systems , vol. 33, no. 6, pp. 2301–2312, 2021.
[58] J. Yu, Y . Lee, K. C. Yow, M. Jeon, and W. Pedrycz, “Abnormal
event detection and localization via adversarial event prediction,” IEEE
Transactions on Neural Networks and Learning Systems , vol. 33, no. 8,
pp. 3572–3586, 2021.
[59] W. Zhou, Y . Li, and C. Zhao, “Object-guided and motion-refined
attention network for video anomaly detection,” in Proceedings of the
IEEE International Conference on Multimedia and Expo , 2022, pp.
1–6.
[60] K. Cheng, X. Zeng, Y . Liu, M. Zhao, C. Pang, and X. Hu, “Spatial-
temporal graph convolutional network boosted flow-frame prediction
for video anomaly detection,” in Proceedings of the IEEE International
Conference on Acoustics, Speech, and Signal Processing , 2023, pp. 1–
5.
[61] Y . Liu, J. Liu, K. Yang, B. Ju, S. Liu, Y . Wang, D. Yang, P. Sun,
and L. Song, “Amp-net: appearance-motion prototype network assisted
automatic video anomaly detection system,” IEEE Transactions on
Industrial Informatics , vol. 20, no. 2, pp. 2843–2855, 2023.
[62] G. Yu, S. Wang, Z. Cai, E. Zhu, C. Xu, J. Yin, and M. Kloft, “Cloze
test helps: effective video anomaly detection via learning to complete
video events,” in Proceedings of the ACM International Conference on
Multimedia , 2020, pp. 583–591.
[63] Z. Yang, J. Liu, Z. Wu, P. Wu, and X. Liu, “Video event restoration
based on keyframes for video anomaly detection,” in Proceedings of
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 18
the IEEE Conference on Computer Vision and Pattern Recognition ,
2023, pp. 14 592–14 601.
[64] G. Yu, S. Wang, Z. Cai, X. Liu, E. Zhu, and J. Yin, “Video anomaly
detection via visual cloze tests,” IEEE Transactions on Information
Forensics and Security , vol. 18, pp. 4955–4969, 2023.
[65] G. Wang, Y . Wang, J. Qin, D. Zhang, X. Bao, and D. Huang,
“Video anomaly detection by solving decoupled spatio-temporal jigsaw
puzzles,” in Proceedings of the European Conference on Computer
Vision , 2022, pp. 494–511.
[66] C. Shi, C. Sun, Y . Wu, and Y . Jia, “Video anomaly detection via
sequentially learning multiple pretext tasks,” in Proceedings of the
IEEE International Conference on Computer Vision , 2023, pp. 10 330–
10 340.
[67] A. Barbalau, R. T. Ionescu, M.-I. Georgescu, J. Dueholm, B. Ra-
machandra, K. Nasrollahi, F. S. Khan, T. B. Moeslund, and M. Shah,
“Ssmtl++: revisiting self-supervised multi-task learning for video
anomaly detection,” Computer Vision and Image Understanding , vol.
229, p. 103656, 2023.
[68] C. Huang, Z. Wu, J. Wen, Y . Xu, Q. Jiang, and Y . Wang, “Abnormal
event detection using deep contrastive learning for intelligent video
surveillance system,” IEEE Transactions on Industrial Informatics ,
vol. 18, no. 8, pp. 5171–5179, 2021.
[69] Z. Wang, Y . Zou, and Z. Zhang, “Cluster attention contrast for
video anomaly detection,” in Proceedings of the ACM International
Conference on Multimedia , 2020, pp. 2463–2471.
[70] Y . Lu, C. Cao, Y . Zhang, and Y . Zhang, “Learnable locality-sensitive
hashing for video anomaly detection,” IEEE Transactions on Circuits
and Systems for Video Technology , vol. 33, no. 2, pp. 963–976, 2022.
[71] C. Sun, Y . Jia, H. Song, and Y . Wu, “Adversarial 3d convolutional auto-
encoder for abnormal event detection in videos,” IEEE Transactions on
Multimedia , vol. 23, pp. 3292–3305, 2020.
[72] D. Chen, L. Yue, X. Chang, M. Xu, and T. Jia, “Nm-gan: noise-
modulated generative adversarial network for video anomaly detec-
tion,” Pattern Recognition , vol. 116, p. 107969, 2021.
[73] Y . Cong, J. Yuan, and J. Liu, “Sparse reconstruction cost for abnormal
event detection,” in Proceedings of the IEEE Conference on Computer
Vision and Pattern Recognition , 2011, pp. 3449–3456.
[74] W. Luo, W. Liu, and S. Gao, “A revisit of sparse coding based anomaly
detection in stacked rnn framework,” in Proceedings of the IEEE
International Conference on Computer Vision , 2017, pp. 341–349.
[75] J. T. Zhou, J. Du, H. Zhu, X. Peng, Y . Liu, and R. S. M. Goh,
“Anomalynet: an anomaly detection network for video surveillance,”
IEEE Transactions on Information Forensics and Security , vol. 14,
no. 10, pp. 2537–2550, 2019.
[76] W. Luo, W. Liu, D. Lian, J. Tang, L. Duan, X. Peng, and S. Gao, “Video
anomaly detection with sparse coding inspired deep neural networks,”
IEEE Transactions on Pattern Analysis and Machine Intelligence ,
vol. 43, no. 3, pp. 1070–1084, 2019.
[77] V . Zavrtanik, M. Kristan, and D. Sko ˇcaj, “Reconstruction by inpainting
for visual anomaly detection,” Pattern Recognition , vol. 112, p. 107706,
2021.
[78] N.-C. Ristea, N. Madan, R. T. Ionescu, K. Nasrollahi, F. S. Khan,
T. B. Moeslund, and M. Shah, “Self-supervised predictive convolutional
attentive block for anomaly detection,” in Proceedings of the IEEE
Conference on Computer Vision and Pattern Recognition , 2022, pp.
13 576–13 586.
[79] N. Madan, N.-C. Ristea, R. T. Ionescu, K. Nasrollahi, F. S. Khan, T. B.
Moeslund, and M. Shah, “Self-supervised masked convolutional trans-
former block for anomaly detection,” IEEE Transactions on Pattern
Analysis and Machine Intelligence , vol. 46, no. 1, pp. 525–542, 2023.
[80] N.-C. Ristea, F.-A. Croitoru, R. T. Ionescu, M. Popescu, F. S. Khan,
M. Shah et al. , “Self-distilled masked auto-encoders are efficient
video anomaly detectors,” in Proceedings of the IEEE Conference on
Computer Vision and Pattern Recognition , 2024, pp. 15 984–15 995.
[81] M. Ye, X. Peng, W. Gan, W. Wu, and Y . Qiao, “Anopcn: video anomaly
detection via deep predictive coding network,” in Proceedings of the
ACM International Conference on Multimedia , 2019, pp. 1805–1813.
[82] Y . Liu, J. Liu, J. Lin, M. Zhao, and L. Song, “Appearance-motion
united auto-encoder framework for video anomaly detection,” IEEE
Transactions on Circuits and Systems II: Express Briefs , vol. 69, no. 5,
pp. 2498–2502, 2022.
[83] Y . Liu, J. Liu, M. Zhao, D. Yang, X. Zhu, and L. Song, “Learning
appearance-motion normality for video anomaly detection,” in Proceed-
ings of the IEEE International Conference on Multimedia and Expo ,
2022, pp. 1–6.
[84] C. Huang, J. Wen, Y . Xu, Q. Jiang, J. Yang, Y . Wang, and D. Zhang,
“Self-supervised attentive generative adversarial networks for videoanomaly detection,” IEEE Transactions on Neural Networks and Learn-
ing Systems , vol. 34, no. 11, pp. 9389–9403, 2022.
[85] M.-I. Georgescu, A. Barbalau, R. T. Ionescu, F. S. Khan, M. Popescu,
and M. Shah, “Anomaly detection in video via self-supervised and
multi-task learning,” in Proceedings of the IEEE Conference on Com-
puter Vision and Pattern Recognition , 2021, pp. 12 742–12 752.
[86] M. Zhang, J. Wang, Q. Qi, H. Sun, Z. Zhuang, P. Ren, R. Ma,
and J. Liao, “Multi-scale video anomaly detection by multi-grained
spatio-temporal representation learning,” in Proceedings of the IEEE
Conference on Computer Vision and Pattern Recognition , 2024, pp.
17 385–17 394.
[87] B. Sch ¨olkopf, J. C. Platt, J. Shawe-Taylor, A. J. Smola, and R. C.
Williamson, “Estimating the support of a high-dimensional distribu-
tion,” Neural Computation , vol. 13, no. 7, pp. 1443–1471, 2001.
[88] D. M. Tax and R. P. Duin, “Support vector data description,” Machine
Learning , vol. 54, pp. 45–66, 2004.
[89] J. Wang and A. Cherian, “Gods: generalized one-class discriminative
subspaces for anomaly detection,” in Proceedings of the IEEE Inter-
national Conference on Computer Vision , 2019, pp. 8201–8211.
[90] L. Ruff, R. Vandermeulen, N. Goernitz, L. Deecke, S. A. Siddiqui,
A. Binder, E. M ¨uller, and M. Kloft, “Deep one-class classification,”
inProceedings of the International Conference on Machine Learning ,
2018, pp. 4393–4402.
[91] P. Liznerski, L. Ruff, R. A. Vandermeulen, B. J. Franks, M. Kloft,
and K.-R. M ¨uller, “Explainable deep one-class classification,” in Pro-
ceedings of the International Conference on Learning Representations ,
2021.
[92] M. Sabokrou, M. Fayyaz, M. Fathy, Z. Moayed, and R. Klette, “Deep-
anomaly: fully convolutional neural network for fast anomaly detection
in crowded scenes,” Computer Vision and Image Understanding , vol.
172, pp. 88–97, 2018.
[93] M. Sabokrou, M. Khalooei, M. Fathy, and E. Adeli, “Adversarially
learned one-class classifier for novelty detection,” in Proceedings of
the IEEE Conference on Computer Vision and Pattern Recognition ,
2018, pp. 3379–3388.
[94] M. Sabokrou, M. Pourreza, M. Fayyaz, R. Entezari, M. Fathy, J. Gall,
and E. Adeli, “Avid: adversarial visual irregularity detection,” in
Proceedings of the Asian Conference on Computer Vision , 2018, pp.
488–505.
[95] M. Sabokrou, M. Fathy, G. Zhao, and E. Adeli, “Deep end-to-end one-
class classifier,” IEEE Transactions on Neural Networks and Learning
Systems , vol. 32, no. 2, pp. 675–684, 2020.
[96] M. Z. Zaheer, J.-h. Lee, M. Astrid, and S.-I. Lee, “Old is gold: redefin-
ing the adversarially learned one-class classifier training paradigm,” in
Proceedings of the IEEE Conference on Computer Vision and Pattern
Recognition , 2020, pp. 14 183–14 193.
[97] M. Z. Zaheer, J.-H. Lee, A. Mahmood, M. Astrid, and S.-I. Lee,
“Stabilizing adversarially learned one-class novelty detection using
pseudo anomalies,” IEEE Transactions on Image Processing , vol. 31,
pp. 5963–5975, 2022.
[98] K. Doshi and Y . Yilmaz, “Towards interpretable video anomaly detec-
tion,” in Proceedings of the IEEE Winter Conference on Applications
of Computer Vision , 2023, pp. 2655–2664.
[99] A. Singh, M. J. Jones, and E. G. Learned-Miller, “Eval: explainable
video anomaly localization,” in Proceedings of the IEEE Conference
on Computer Vision and Pattern Recognition , 2023, pp. 18 717–18 726.
[100] Y . Yang, K. Lee, B. Dariush, Y . Cao, and S.-Y . Lo, “Follow the rules:
reasoning for video anomaly detection with large language models,” in
Proceedings of the European Conference on Computer Vision , 2024.
[101] J. R. Medel and A. Savakis, “Anomaly detection in video using
predictive convolutional long short-term memory networks,” arXiv
preprint arXiv:1612.00390 , 2016.
[102] M. Ravanbakhsh, E. Sangineto, M. Nabi, and N. Sebe, “Training
adversarial discriminators for cross-channel abnormal event detection
in crowds,” in Proceedings of the IEEE Winter Conference on Appli-
cations of Computer Vision , 2019, pp. 1896–1904.
[103] H. Vu, T. D. Nguyen, T. Le, W. Luo, and D. Phung, “Robust anomaly
detection in videos using multilevel representations,” in Proceedings of
the AAAI Conference on Artificial Intelligence , vol. 33, no. 01, 2019,
pp. 5216–5223.
[104] H. Song, C. Sun, X. Wu, M. Chen, and Y . Jia, “Learning normal
patterns via adversarial attention-based autoencoder for abnormal event
detection in videos,” IEEE Transactions on Multimedia , vol. 22, no. 8,
pp. 2138–2148, 2019.
[105] X. Feng, D. Song, Y . Chen, Z. Chen, J. Ni, and H. Chen, “Convolutional
transformer based dual discriminator generative adversarial networks
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 19
for video anomaly detection,” in Proceedings of the ACM International
Conference on Multimedia , 2021, pp. 5546–5554.
[106] P. Wu, W. Wang, F. Chang, C. Liu, and B. Wang, “Dss-net: dynamic
self-supervised network for video anomaly detection,” IEEE Transac-
tions on Multimedia , vol. 26, pp. 2124–2136, 2023.
[107] M. Astrid, M. Z. Zaheer, and S.-I. Lee, “Limiting reconstruction
capability of autoencoders using moving backward pseudo anomalies,”
inProceedings of the International Conference on Ubiquitous Robots ,
2022, pp. 248–251.
[108] M. Astrid, M. Zaheer, J.-Y . Lee, and S.-I. Lee, “Learning not to
reconstruct anomalies,” in Proceedings of the British Machine Vision
Conference , 2021.
[109] M. Astrid, M. Z. Zaheer, and S.-I. Lee, “Pseudobound: limiting the
anomaly reconstruction capability of one-class classifiers using pseudo
anomalies,” Neurocomputing , vol. 534, pp. 147–160, 2023.
[110] M. Pourreza, B. Mohammadi, M. Khaki, S. Bouindour, H. Snoussi,
and M. Sabokrou, “G2d: generate to detect anomaly,” in Proceedings
of the IEEE Winter Conference on Applications of Computer Vision ,
2021, pp. 2003–2012.
[111] M. I. Georgescu, R. T. Ionescu, F. S. Khan, M. Popescu, and M. Shah,
“A background-agnostic framework with adversarial training for abnor-
mal event detection in video,” IEEE Transactions on Pattern Analysis
and Machine Intelligence , vol. 44, no. 9, pp. 4505–4523, 2021.
[112] Z. Liu, X.-M. Wu, D. Zheng, K.-Y . Lin, and W.-S. Zheng, “Generating
anomalies for video anomaly detection with prompt-based feature
mapping,” in Proceedings of the IEEE Conference on Computer Vision
and Pattern Recognition , 2023, pp. 24 500–24 510.
[113] J. Leng, M. Tan, X. Gao, W. Lu, and Z. Xu, “Anomaly warning:
learning and memorizing future semantic patterns for unsupervised
ex-ante potential anomaly prediction,” in Proceedings of the ACM
International Conference on Multimedia , 2022, pp. 6746–6754.
[114] G. Yu, S. Wang, Z. Cai, X. Liu, and C. Wu, “Effective video abnormal
event detection by learning a consistency-aware high-level feature
extractor,” in Proceedings of the ACM International Conference on
Multimedia , 2022, pp. 6337–6346.
[115] W. Liu, H. Chang, B. Ma, S. Shan, and X. Chen, “Diversity-measurable
anomaly detection,” in Proceedings of the IEEE Conference on Com-
puter Vision and Pattern Recognition , 2023, pp. 12 147–12 156.
[116] Y . Liu, D. Yang, G. Fang, Y . Wang, D. Wei, M. Zhao, K. Cheng, J. Liu,
and L. Song, “Stochastic video normality network for abnormal event
detection in surveillance videos,” Knowledge-Based Systems , vol. 280,
p. 110986, 2023.
[117] C. Sun, C. Shi, Y . Jia, and Y . Wu, “Learning event-relevant factors for
video anomaly detection,” in Proceedings of the AAAI Conference on
Artificial Intelligence , vol. 37, no. 2, 2023, pp. 2384–2392.
[118] L. Wang, J. Tian, S. Zhou, H. Shi, and G. Hua, “Memory-augmented
appearance-motion network for video anomaly detection,” Pattern
Recognition , vol. 138, p. 109335, 2023.
[119] D. Gong, L. Liu, V . Le, B. Saha, M. R. Mansour, S. Venkatesh, and
A. v. d. Hengel, “Memorizing normality to detect anomaly: memory-
augmented deep autoencoder for unsupervised anomaly detection,” in
Proceedings of the IEEE International Conference on Computer Vision ,
2019, pp. 1705–1714.
[120] H. Park, J. Noh, and B. Ham, “Learning memory-guided normality
for anomaly detection,” in Proceedings of the IEEE Conference on
Computer Vision and Pattern Recognition , 2020, pp. 14 372–14 381.
[121] H. Lv, C. Chen, Z. Cui, C. Xu, Y . Li, and J. Yang, “Learning normal
dynamics in videos with meta prototype network,” in Proceedings of
the IEEE Conference on Computer Vision and Pattern Recognition ,
2021, pp. 15 425–15 434.
[122] Z. Yang, P. Wu, J. Liu, and X. Liu, “Dynamic local aggregation network
with adaptive clusterer for anomaly detection,” in Proceedings of the
European Conference on Computer Vision , 2022, pp. 404–421.
[123] C. Cao, Y . Lu, and Y . Zhang, “Context recovery and knowledge
retrieval: a novel two-stream framework for video anomaly detection,”
IEEE Transactions on Image Processing , vol. 33, pp. 1810–1825, 2024.
[124] S. Lee, H. G. Kim, and Y . M. Ro, “Bman: bidirectional multi-scale
aggregation networks for abnormal event detection,” IEEE Transactions
on Image Processing , vol. 29, pp. 2395–2408, 2019.
[125] C. Cao, Y . Lu, P. Wang, and Y . Zhang, “A new comprehensive bench-
mark for semi-supervised video anomaly detection and anticipation,” in
Proceedings of the IEEE Conference on Computer Vision and Pattern
Recognition , 2023, pp. 20 392–20 401.
[126] C. Huang, C. Liu, Z. Zhang, Z. Wu, J. Wen, Q. Jiang, and Y . Xu, “Pixel-
level anomaly detection via uncertainty-aware prototypical trans-
former,” in Proceedings of the ACM International Conference on
Multimedia , 2022, pp. 521–530.[127] D. Tran, L. Bourdev, R. Fergus, L. Torresani, and M. Paluri, “Learning
spatiotemporal features with 3d convolutional networks,” in Proceed-
ings of the IEEE International Conference on Computer Vision , 2015,
pp. 4489–4497.
[128] M. Z. Zaheer, A. Mahmood, M. Astrid, and S.-I. Lee, “Claws:
clustering assisted weakly supervised learning with normalcy suppres-
sion for anomalous event detection,” in Proceedings of the European
Conference on Computer Vision , 2020, pp. 358–376.
[129] J. Carreira and A. Zisserman, “Quo vadis, action recognition? a new
model and the kinetics dataset,” in Proceedings of the IEEE Conference
on Computer Vision and Pattern Recognition , 2017, pp. 6299–6308.
[130] P. Wu, J. Liu, Y . Shi, Y . Sun, F. Shao, Z. Wu, and Z. Yang, “Not
only look, but also listen: learning multimodal violence detection under
weak supervision,” in Proceedings of the European Conference on
Computer Vision , 2020, pp. 322–339.
[131] J.-C. Wu, H.-Y . Hsieh, D.-J. Chen, C.-S. Fuh, and T.-L. Liu, “Self-
supervised sparse representation for video anomaly detection,” in
Proceedings of the European Conference on Computer Vision , 2022,
pp. 729–745.
[132] Y . Zhou, Y . Qu, X. Xu, F. Shen, J. Song, and H. Shen, “Batchnorm-
based weakly supervised video anomaly detection,” arXiv preprint
arXiv:2311.15367 , 2023.
[133] S. AlMarri, M. Z. Zaheer, and K. Nandakumar, “A multi-head approach
with shuffled segments for weakly-supervised video anomaly detec-
tion,” in Proceedings of the IEEE Winter Conference on Applications
of Computer Vision , 2024, pp. 132–142.
[134] K. Hara, H. Kataoka, and Y . Satoh, “Can spatiotemporal 3d cnns retrace
the history of 2d cnns and imagenet?” in Proceedings of the IEEE
Conference on Computer Vision and Pattern Recognition , 2018, pp.
6546–6555.
[135] S. Sun and X. Gong, “Long-short temporal co-teaching for weakly
supervised video anomaly detection,” in Proceedings of the IEEE
International Conference on Multimedia and Expo , 2023, pp. 2711–
2716.
[136] L. Wang, Y . Xiong, Z. Wang, Y . Qiao, D. Lin, X. Tang, and
L. Van Gool, “Temporal segment networks: towards good practices for
deep action recognition,” in Proceedings of the European Conference
on Computer Vision , 2016, pp. 20–36.
[137] J.-X. Zhong, N. Li, W. Kong, S. Liu, T. H. Li, and G. Li, “Graph
convolutional label noise cleaner: train a plug-and-play action classifier
for anomaly detection,” in Proceedings of the IEEE Conference on
Computer Vision and Pattern Recognition , 2019, pp. 1237–1246.
[138] N. Li, J.-X. Zhong, X. Shu, and H. Guo, “Weakly-supervised anomaly
detection in video surveillance via graph convolutional label noise
cleaning,” Neurocomputing , vol. 481, pp. 154–167, 2022.
[139] Z. Liu, Y . Lin, Y . Cao, H. Hu, Y . Wei, Z. Zhang, S. Lin, and
B. Guo, “Swin transformer: hierarchical vision transformer using
shifted windows,” in Proceedings of the IEEE International Conference
on Computer Vision , 2021, pp. 10 012–10 022.
[140] S. Li, F. Liu, and L. Jiao, “Self-training multi-sequence learning
with transformer for weakly supervised video anomaly detection,” in
Proceedings of the AAAI Conference on Artificial Intelligence , vol. 36,
no. 2, 2022, pp. 1395–1403.
[141] A. Radford, J. W. Kim, C. Hallacy, A. Ramesh, G. Goh, S. Agarwal,
G. Sastry, A. Askell, P. Mishkin, J. Clark et al. , “Learning transferable
visual models from natural language supervision,” in Proceedings of
the International Conference on Machine Learning , 2021, pp. 8748–
8763.
[142] P. Wu, X. Zhou, G. Pang, Y . Sun, J. Liu, P. Wang, and Y . Zhang,
“Open-vocabulary video anomaly detection,” in Proceedings of the
IEEE Conference on Computer Vision and Pattern Recognition , 2024,
pp. 18 297–18 307.
[143] B. Wan, Y . Fang, X. Xia, and J. Mei, “Weakly supervised video
anomaly detection via center-guided discriminative learning,” in Pro-
ceedings of the IEEE International Conference on Multimedia and
Expo , 2020, pp. 1–6.
[144] S. Hershey, S. Chaudhuri, D. P. Ellis, J. F. Gemmeke, A. Jansen, R. C.
Moore, M. Plakal, D. Platt, R. A. Saurous, B. Seybold et al. , “Cnn
architectures for large-scale audio classification,” in Proceedings of
the IEEE International Conference on Acoustics, Speech, and Signal
Processing , 2017, pp. 131–135.
[145] W.-F. Pang, Q.-H. He, Y .-j. Hu, and Y .-X. Li, “Violence detection in
videos based on fusing visual and audio information,” in Proceedings
of the IEEE International Conference on Acoustics, Speech, and Signal
Processing , 2021, pp. 2260–2264.
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 20
[146] X. Peng, H. Wen, Y . Luo, X. Zhou, K. Yu, Y . Wang, and Z. Wu,
“Learning weakly supervised audio-visual violence detection in hyper-
bolic space,” arXiv preprint arXiv:2305.18797 , 2023.
[147] Y . Pu, X. Wu, and S. Wang, “Learning prompt-enhanced context fea-
tures for weakly-supervised video anomaly detection,” arXiv preprint
arXiv:2306.14451 , 2023.
[148] W. Chen, K. T. Ma, Z. J. Yew, M. Hur, and D. A.-A. Khoo, “Tevad:
improved video anomaly detection with captions,” in Proceedings of
the IEEE Conference on Computer Vision and Pattern Recognition
Workshop , 2023, pp. 5548–5558.
[149] C. Tao, C. Wang, Y . Zou, X. Peng, J. Wu, and J. Qian, “Learn suspected
anomalies from event prompts for video anomaly detection,” arXiv
preprint arXiv:2403.01169 , 2024.
[150] P. Wu, J. Liu, X. He, Y . Peng, P. Wang, and Y . Zhang, “Toward video
anomaly retrieval from video anomaly detection: new benchmarks and
model,” IEEE Transactions on Image Processing , vol. 33, pp. 2213–
2225, 2024.
[151] D.-L. Wei, C.-G. Liu, Y . Liu, J. Liu, X.-G. Zhu, and X.-H. Zeng,
“Look, listen and pay more attention: fusing multi-modal information
for video violence detection,” in Proceedings of the IEEE International
Conference on Acoustics, Speech, and Signal Processing , 2022, pp.
1980–1984.
[152] D. Wei, Y . Liu, X. Zhu, J. Liu, and X. Zeng, “Msaf: multimodal
supervise-attention enhanced fusion for video anomaly detection,”
IEEE Signal Processing Letters , vol. 29, pp. 2178–2182, 2022.
[153] J. Yu, J. Liu, Y . Cheng, R. Feng, and Y . Zhang, “Modality-aware con-
trastive instance learning with self-distillation for weakly-supervised
audio-visual violence detection,” in Proceedings of the ACM Interna-
tional Conference on Multimedia , 2022, pp. 6278–6287.
[154] P. Wu, X. Liu, and J. Liu, “Weakly supervised audio-visual violence
detection,” IEEE Transactions on Multimedia , vol. 25, pp. 1674–1685,
2022.
[155] T. Yuan, X. Zhang, K. Liu, B. Liu, C. Chen, J. Jin, and Z. Jiao,
“Towards surveillance video-and-language understanding: new dataset
baselines and challenges,” in Proceedings of the IEEE Conference on
Computer Vision and Pattern Recognition , 2024, pp. 22 052–22 061.
[156] Y . Zhu and S. Newsam, “Motion-aware feature for improved video
anomaly detection,” in Proceedings of the British Machine Vision
Conference , 2019.
[157] J. Zhang, L. Qing, and J. Miao, “Temporal convolutional network
with complementary inner bag loss for weakly supervised anomaly
detection,” in Proceedings of the IEEE International Conference on
Image Processing , 2019, pp. 4030–4034.
[158] Y . Liu, J. Liu, M. Zhao, S. Li, and L. Song, “Collaborative normality
learning framework for weakly supervised video anomaly detection,”
IEEE Transactions on Circuits and Systems II: Express Briefs , vol. 69,
no. 5, pp. 2508–2512, 2022.
[159] J.-C. Feng, F.-T. Hong, and W.-S. Zheng, “Mist: multiple instance self-
training framework for video anomaly detection,” in Proceedings of the
IEEE Conference on Computer Vision and Pattern Recognition , 2021,
pp. 14 009–14 018.
[160] C. Zhang, G. Li, Y . Qi, S. Wang, L. Qing, Q. Huang, and M.-H.
Yang, “Exploiting completeness and uncertainty of pseudo labels for
weakly supervised video anomaly detection,” in Proceedings of the
IEEE Conference on Computer Vision and Pattern Recognition , 2023,
pp. 16 271–16 280.
[161] Z. Yang, J. Liu, and P. Wu, “Text prompt with normality guidance
for weakly supervised video anomaly detection,” in Proceedings of the
IEEE Conference on Computer Vision and Pattern Recognition , 2024,
pp. 18 899–18 908.
[162] P. Wu and J. Liu, “Learning causal temporal relation and feature
discrimination for anomaly detection,” IEEE Transactions on Image
Processing , vol. 30, pp. 3513–3527, 2021.
[163] Y . Tian, G. Pang, Y . Chen, R. Singh, J. W. Verjans, and G. Carneiro,
“Weakly-supervised video anomaly detection with robust temporal
feature magnitude learning,” in Proceedings of the IEEE International
Conference on Computer Vision , 2021, pp. 4975–4986.
[164] T. Liu, C. Zhang, K.-M. Lam, and J. Kong, “Decouple and resolve:
transformer-based models for online anomaly detection from weakly
labeled videos,” IEEE Transactions on Information Forensics and
Security , vol. 18, pp. 15–28, 2022.
[165] S. Chang, Y . Li, S. Shen, J. Feng, and Z. Zhou, “Contrastive attention
for video anomaly detection,” IEEE Transactions on Multimedia ,
vol. 24, pp. 4067–4076, 2021.
[166] M. Cho, M. Kim, S. Hwang, C. Park, K. Lee, and S. Lee, “Look around
for anomalies: weakly-supervised anomaly detection via context-motion relational learning,” in Proceedings of the IEEE Conference on
Computer Vision and Pattern Recognition , 2023, pp. 12 137–12 146.
[167] D. Purwanto, Y .-T. Chen, and W.-H. Fang, “Dance with self-attention: a
new look of conditional random fields on anomaly detection in videos,”
inProceedings of the IEEE International Conference on Computer
Vision , 2021, pp. 173–183.
[168] C. Huang, C. Liu, J. Wen, L. Wu, Y . Xu, Q. Jiang, and Y . Wang,
“Weakly supervised video anomaly detection via self-guided temporal
discriminative transformer,” IEEE Transactions on Cybernetics , vol. 54,
no. 5, pp. 3197–3210, 2022.
[169] C. Zhang, G. Li, Q. Xu, X. Zhang, L. Su, and Q. Huang, “Weakly
supervised anomaly detection in videos considering the openness of
events,” IEEE Transactions on Intelligent Transportation Systems ,
vol. 23, no. 11, pp. 21 687–21 699, 2022.
[170] H. Zhou, J. Yu, and W. Yang, “Dual memory units with uncertainty
regulation for weakly supervised video anomaly detection,” in Proceed-
ings of the AAAI Conference on Artificial Intelligence , vol. 37, no. 3,
2023, pp. 3769–3777.
[171] G. Li, G. Cai, X. Zeng, and R. Zhao, “Scale-aware spatio-temporal
relation learning for video anomaly detection,” in Proceedings of the
European Conference on Computer Vision , 2022, pp. 333–350.
[172] H. Ye, K. Xu, X. Jiang, and T. Sun, “Learning spatio-temporal relations
with multi-scale integrated perception for video anomaly detection,”
inProceedings of the IEEE International Conference on Acoustics,
Speech, and Signal Processing , 2024, pp. 4020–4024.
[173] S. Lin, H. Yang, X. Tang, T. Shi, and L. Chen, “Social mil: interaction-
aware for crowd anomaly detection,” in Proceedings of the IEEE
International Conference on Advanced Video and Signal-Based Surveil-
lance , 2019, pp. 1–8.
[174] S. Park, H. Kim, M. Kim, D. Kim, and K. Sohn, “Normality guided
multiple instance learning for weakly supervised video anomaly detec-
tion,” in Proceedings of the IEEE Winter Conference on Applications
of Computer Vision , 2023, pp. 2665–2674.
[175] H. Lv, Z. Yue, Q. Sun, B. Luo, Z. Cui, and H. Zhang, “Unbiased
multiple instance learning for weakly supervised video anomaly detec-
tion,” in Proceedings of the IEEE Conference on Computer Vision and
Pattern Recognition , 2023, pp. 8022–8031.
[176] J. Chen, L. Li, L. Su, Z.-j. Zha, and Q. Huang, “Prompt-enhanced
multiple instance learning for weakly supervised video anomaly detec-
tion,” in Proceedings of the IEEE Conference on Computer Vision and
Pattern Recognition , 2024, pp. 18 319–18 329.
[177] Y . Chen, Z. Liu, B. Zhang, W. Fok, X. Qi, and Y .-C. Wu, “Mgfn:
magnitude-contrastive glance-and-focus network for weakly-supervised
video anomaly detection,” in Proceedings of the AAAI Conference on
Artificial Intelligence , vol. 37, no. 1, 2023, pp. 387–395.
[178] Y . Gong, C. Wang, X. Dai, S. Yu, L. Xiang, and J. Wu, “Multi-
scale continuity-aware refinement network for weakly supervised video
anomaly detection,” in Proceedings of the IEEE International Confer-
ence on Multimedia and Expo , 2022, pp. 1–6.
[179] H. Sapkota and Q. Yu, “Bayesian nonparametric submodular video
partition for robust anomaly detection,” in Proceedings of the IEEE
Conference on Computer Vision and Pattern Recognition , 2022, pp.
3212–3221.
[180] J. Fioresi, I. R. Dave, and M. Shah, “Ted-spad: temporal distinctiveness
for self-supervised privacy-preservation for video anomaly detection,”
inProceedings of the IEEE International Conference on Computer
Vision , 2023, pp. 13 598–13 609.
[181] M. Z. Zaheer, A. Mahmood, M. Astrid, and S.-I. Lee, “Clustering aided
weakly supervised training to detect anomalous events in surveillance
videos,” IEEE Transactions on Neural Networks and Learning Systems ,
pp. 1–14, 2023.
[182] T. Liu, K.-M. Lam, and J. Kong, “Distilling privileged knowledge
for anomalous event detection from weakly labeled videos,” IEEE
Transactions on Neural Networks and Learning Systems , pp. 1–15,
2023.
[183] P. Wu, X. Zhou, G. Pang, L. Zhou, Q. Yan, P. Wang, and Y . Zhang,
“Vadclip: adapting vision-language models for weakly supervised video
anomaly detection,” in Proceedings of the AAAI Conference on Artifi-
cial Intelligence , vol. 38, no. 6, 2024, pp. 6074–6082.
[184] P. Wu, X. Zhou, G. Pang, Z. Yang, Q. Yan, P. Wang, and Y . Zhang,
“Weakly supervised video anomaly detection and localization with
spatio-temporal prompts,” in Proceedings of the ACM International
Conference on Multimedia , 2024.
[185] H. K. Joo, K. V o, K. Yamazaki, and N. Le, “Clip-tsa: clip-assisted
temporal self-attention for weakly-supervised video anomaly detec-
tion,” in Proceedings of the IEEE International Conference on Image
Processing , 2023, pp. 3230–3234.
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 21
[186] H. Zhang, X. Xu, X. Wang, J. Zuo, C. Han, X. Huang, C. Gao,
Y . Wang, and N. Sang, “Holmes-vad: towards unbiased and explain-
able video anomaly detection via multi-modal llm,” arXiv preprint
arXiv:2406.12235 , 2024.
[187] H. Lv and Q. Sun, “Video anomaly detection and explanation via large
language models,” arXiv preprint arXiv:2401.05702 , 2024.
[188] L. Zanella, W. Menapace, M. Mancini, Y . Wang, and E. Ricci,
“Harnessing large language models for training-free video anomaly
detection,” in Proceedings of the IEEE Conference on Computer Vision
and Pattern Recognition , 2024, pp. 18 527–18 536.
[189] J. Jeong, Y . Zou, T. Kim, D. Zhang, A. Ravichandran, and O. Dabeer,
“Winclip: Zero-/few-shot anomaly classification and segmentation,” in
Proceedings of the IEEE Conference on Computer Vision and Pattern
Recognition , 2023, pp. 19 606–19 616.
[190] Q. Zhou, G. Pang, Y . Tian, S. He, and J. Chen, “Anomalyclip: Object-
agnostic prompt learning for zero-shot anomaly detection,” in The
Twelfth International Conference on Learning Representations , 2024.
[191] J. Zhu and G. Pang, “Toward generalist anomaly detection via in-
context residual learning with few-shot sample prompts,” in Pro-
ceedings of the IEEE Conference on Computer Vision and Pattern
Recognition , 2024, pp. 17 826–17 836.
[192] K. Liu and H. Ma, “Exploring background-bias for anomaly detection
in surveillance videos,” in Proceedings of the ACM International
Conference on Multimedia , 2019, pp. 1490–1499.
[193] J. Wu, W. Zhang, G. Li, W. Wu, X. Tan, Y . Li, E. Ding, and L. Lin,
“Weakly-supervised spatio-temporal anomaly detection in surveillance
video,” in Proceedings of International Joint Conferences on Artificial
Intelligence , 2021.
[194] Z. Dong, J. Qin, and Y . Wang, “Multi-stream deep networks for person
to person violence detection in videos,” in Proceedings of the Chinese
Conference on Pattern Recognition , 2016, pp. 517–531.
[195] P. Zhou, Q. Ding, H. Luo, and X. Hou, “Violent interaction detection
in video based on deep learning,” in Journal of Physics: Conference
Series , vol. 844, no. 1, 2017, p. 012044.
[196] B. Peixoto, B. Lavi, J. P. P. Martin, S. Avila, Z. Dias, and A. Rocha,
“Toward subjective violence detection in videos,” in Proceedings of
the IEEE International Conference on Acoustics, Speech, and Signal
Processing , 2019, pp. 8276–8280.
[197] M. Perez, A. C. Kot, and A. Rocha, “Detection of real-world fights
in surveillance videos,” in Proceedings of the IEEE International
Conference on Acoustics, Speech, and Signal Processing , 2019, pp.
2662–2666.
[198] B. Peixoto, B. Lavi, P. Bestagini, Z. Dias, and A. Rocha, “Multimodal
violence detection in videos,” in Proceedings of the IEEE International
Conference on Acoustics, Speech, and Signal Processing , 2020, pp.
2957–2961.
[199] S. Sudhakaran and O. Lanz, “Learning to detect violent videos using
convolutional long short-term memory,” in Proceedings of the IEEE
International Conference on Advanced Video and Signal-Based Surveil-
lance , 2017, pp. 1–6.
[200] A. Hanson, K. Pnvr, S. Krishnagopal, and L. Davis, “Bidirectional con-
volutional lstm for the detection of violence in videos,” in Proceedings
of the European Conference on Computer Vision Workshop , 2018, pp.
0–0.
[201] Y . Su, G. Lin, J. Zhu, and Q. Wu, “Human interaction learning on 3d
skeleton point clouds for video violence recognition,” in Proceedings
of the European Conference on Computer Vision , 2020, pp. 74–90.
[202] A. Singh, D. Patil, and S. Omkar, “Eye in the sky: real-time drone
surveillance system (dss) for violent individuals identification using
scatternet hybrid deep learning network,” in Proceedings of the IEEE
Conference on Computer Vision and Pattern Recognition Workshop ,
2018, pp. 1629–1637.
[203] M. Cheng, K. Cai, and M. Li, “Rwf-2000: an open large scale video
database for violence detection,” in Proceedings of the International
Conference on Pattern Recognition , 2021, pp. 4183–4190.
[204] Y . Shang, X. Wu, and R. Liu, “Multimodal violent video recognition
based on mutual distillation,” in Proceedings of the Chinese Conference
on Pattern Recognition and Computer Vision , 2022, pp. 623–637.
[205] G. Garcia-Cobo and J. C. SanMiguel, “Human skeletons and change
detection for efficient violence detection in surveillance videos,” Com-
puter Vision and Image Understanding , vol. 233, p. 103739, 2023.
[206] J. Su, P. Her, E. Clemens, E. Yaz, S. Schneider, and H. Medeiros,
“Violence detection using 3d convolutional neural networks,” in Pro-
ceedings of the IEEE International Conference on Advanced Video and
Signal-Based Surveillance , 2022, pp. 1–8.[207] A. Del Giorno, J. A. Bagnell, and M. Hebert, “A discriminative
framework for anomaly detection in large videos,” in Proceedings of
the European Conference on Computer Vision , 2016, pp. 334–349.
[208] R. Tudor Ionescu, S. Smeureanu, B. Alexe, and M. Popescu, “Un-
masking the abnormal events in video,” in Proceedings of the IEEE
International Conference on Computer Vision , 2017, pp. 2895–2903.
[209] Y . Liu, C.-L. Li, and B. P ´oczos, “Classifier two sample test for video
anomaly detections,” in Proceedings of the British Machine Vision
Conference , 2018, p. 71.
[210] S. Wang, Y . Zeng, Q. Liu, C. Zhu, E. Zhu, and J. Yin, “Detecting
abnormality without knowing normality: a two-stage approach for
unsupervised video abnormal event detection,” in Proceedings of the
ACM International Conference on Multimedia , 2018, pp. 636–644.
[211] G. Pang, C. Yan, C. Shen, A. v. d. Hengel, and X. Bai, “Self-trained
deep ordinal regression for end-to-end video anomaly detection,” in
Proceedings of the IEEE Conference on Computer Vision and Pattern
Recognition , 2020, pp. 12 173–12 182.
[212] J. Hu, G. Yu, S. Wang, E. Zhu, Z. Cai, and X. Zhu, “Detecting
anomalous events from unlabeled videos via temporal masked auto-
encoding,” in Proceedings of the IEEE International Conference on
Multimedia and Expo , 2022, pp. 1–6.
[213] X. Lin, Y . Chen, G. Li, and Y . Yu, “A causal inference look at
unsupervised video anomaly detection,” in Proceedings of the AAAI
Conference on Artificial Intelligence , vol. 36, no. 2, 2022, pp. 1620–
1629.
[214] G. Yu, S. Wang, Z. Cai, X. Liu, C. Xu, and C. Wu, “Deep anomaly
discovery from unlabeled videos via normality advantage and self-
paced refinement,” in Proceedings of the IEEE Conference on Com-
puter Vision and Pattern Recognition , 2022, pp. 13 987–13 998.
[215] M. Z. Zaheer, A. Mahmood, M. H. Khan, M. Segu, F. Yu, and S.-I.
Lee, “Generative cooperative learning for unsupervised video anomaly
detection,” in Proceedings of the IEEE Conference on Computer Vision
and Pattern Recognition , 2022, pp. 14 744–14 754.
[216] A. Al-lahham, N. Tastan, M. Z. Zaheer, and K. Nandakumar, “A coarse-
to-fine pseudo-labeling (c2fpl) framework for unsupervised video
anomaly detection,” in Proceedings of the IEEE Winter Conference
on Applications of Computer Vision , 2024, pp. 6793–6802.
[217] K. He, X. Chen, S. Xie, Y . Li, P. Doll ´ar, and R. Girshick, “Masked
autoencoders are scalable vision learners,” in Proceedings of the IEEE
Conference on Computer Vision and Pattern Recognition , 2022, pp.
16 000–16 009.
[218] T. Li, Z. Wang, S. Liu, and W.-Y . Lin, “Deep unsupervised anomaly
detection,” in Proceedings of the IEEE Winter Conference on Applica-
tions of Computer Vision , 2021, pp. 3636–3645.
[219] W. Liu, W. Luo, Z. Li, P. Zhao, S. Gao et al. , “Margin learning
embedded prediction for video anomaly detection with a few anoma-
lies,” in Proceedings of the International Joint Conference on Artificial
Intelligence , 2019, pp. 3023–3030.
[220] A. Acsintoae, A. Florescu, M.-I. Georgescu, T. Mare, P. Sumedrea,
R. T. Ionescu, F. S. Khan, and M. Shah, “Ubnormal: new benchmark
for supervised open-set video anomaly detection,” in Proceedings of
the IEEE Conference on Computer Vision and Pattern Recognition ,
2022, pp. 20 143–20 153.
[221] Y . Zhu, W. Bao, and Q. Yu, “Towards open set video anomaly
detection,” in Proceedings of the European Conference on Computer
Vision , 2022, pp. 395–412.
[222] C. Ding, G. Pang, and C. Shen, “Catching both gray and black swans:
Open-set supervised anomaly detection,” in Proceedings of the IEEE
Conference on Computer Vision and Pattern Recognition , 2022, pp.
7388–7398.
[223] J. Zhu, C. Ding, Y . Tian, and G. Pang, “Anomaly heterogeneity learning
for open-set supervised anomaly detection,” in Proceedings of the IEEE
Conference on Computer Vision and Pattern Recognition , 2024, pp.
17 616–17 626.
[224] Y . Lu, F. Yu, M. K. K. Reddy, and Y . Wang, “Few-shot scene-adaptive
anomaly detection,” in Proceedings of the European Conference on
Computer Vision , 2020, pp. 125–141.
[225] Y . Hu, X. Huang, and X. Luo, “Adaptive anomaly detection network
for unseen scene without fine-tuning,” in Proceedings of the Chinese
Conference on Pattern Recognition and Computer Vision , 2021, pp.
311–323.
[226] X. Huang, Y . Hu, X. Luo, J. Han, B. Zhang, and X. Cao, “Boosting
variational inference with margin learning for few-shot scene-adaptive
anomaly detection,” IEEE Transactions on Circuits and Systems for
Video Technology , vol. 33, no. 6, pp. 2813–2825, 2022.
[227] A. Aich, K.-C. Peng, and A. K. Roy-Chowdhury, “Cross-domain video
anomaly detection without target domain adaptation,” in Proceedings
JOURNAL OF L ATEX CLASS FILES, VOL. 14, NO. 8, AUGUST 2021 22
of the IEEE Winter Conference on Applications of Computer Vision ,
2023, pp. 2579–2591.
[228] W. Li, V . Mahadevan, and N. Vasconcelos, “Anomaly detection and
localization in crowded scenes,” IEEE Transactions on Pattern Analysis
and Machine Intelligence , vol. 36, no. 1, pp. 18–32, 2013.
[229] Z. Sun, Q. Ke, H. Rahmani, M. Bennamoun, G. Wang, and J. Liu,
“Human action recognition from various data modalities: A review,”
IEEE Transactions on Pattern Analysis and Machine Intelligence ,
vol. 45, no. 3, pp. 3200–3225, 2022.
[230] Y . Zhu, Y . Wu, N. Sebe, and Y . Yan, “Vision+x: a survey on multimodal
learning in the light of data,” IEEE Transactions on Pattern Analysis
and Machine Intelligence , 2024.
[231] J. Gui, T. Chen, J. Zhang, Q. Cao, Z. Sun, H. Luo, and D. Tao,
“A survey on self-supervised learning: algorithms, applications, and
future trends,” IEEE Transactions on Pattern Analysis and Machine
Intelligence , 2024.
[232] D. Zhou, Q. Wang, Z. Qi, H. Ye, D. Zhan, and Z. Liu, “Class-
incremental learning: a survey,” IEEE Transactions on Pattern Analysis
and Machine Intelligence , 2024.
